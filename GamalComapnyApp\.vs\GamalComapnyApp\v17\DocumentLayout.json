{"Version": 1, "WorkspaceRootPath": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{2629204D-6E1D-48F6-BBD3-849E0A9546CE}|GamalCompany.Data\\GamalCompany.Data.csproj|d:\\aiprojecttest\\sourcs\\gamalcompanyapp\\gamalcomapnyapp\\gamalcompany.data\\context\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2629204D-6E1D-48F6-BBD3-849E0A9546CE}|GamalCompany.Data\\GamalCompany.Data.csproj|solutionrelative:gamalcompany.data\\context\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2629204D-6E1D-48F6-BBD3-849E0A9546CE}|GamalCompany.Data\\GamalCompany.Data.csproj|d:\\aiprojecttest\\sourcs\\gamalcompanyapp\\gamalcomapnyapp\\gamalcompany.data\\models\\financialtransaction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2629204D-6E1D-48F6-BBD3-849E0A9546CE}|GamalCompany.Data\\GamalCompany.Data.csproj|solutionrelative:gamalcompany.data\\models\\financialtransaction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|d:\\aiprojecttest\\sourcs\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapany.service\\repositories\\implementations\\financialservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|solutionrelative:gamalcomapany.service\\repositories\\implementations\\financialservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|d:\\aiprojecttest\\sourcs\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapany.service\\repositories\\interfaces\\ifinancialrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|solutionrelative:gamalcomapany.service\\repositories\\interfaces\\ifinancialrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3B68E784-7A2A-4FCD-B6F2-7733D7DC56C2}|GamalComapnyApp\\GamalComapnyApp.API.csproj|d:\\aiprojecttest\\sourcs\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapnyapp\\controllers\\financialcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3B68E784-7A2A-4FCD-B6F2-7733D7DC56C2}|GamalComapnyApp\\GamalComapnyApp.API.csproj|solutionrelative:gamalcomapnyapp\\controllers\\financialcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|d:\\aiprojecttest\\sourcs\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapany.service\\repositories\\implementations\\partnerservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|solutionrelative:gamalcomapany.service\\repositories\\implementations\\partnerservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2629204D-6E1D-48F6-BBD3-849E0A9546CE}|GamalCompany.Data\\GamalCompany.Data.csproj|d:\\aiprojecttest\\sourcs\\gamalcompanyapp\\gamalcomapnyapp\\gamalcompany.data\\enum\\refransefinancicalenum.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2629204D-6E1D-48F6-BBD3-849E0A9546CE}|GamalCompany.Data\\GamalCompany.Data.csproj|solutionrelative:gamalcompany.data\\enum\\refransefinancicalenum.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2629204D-6E1D-48F6-BBD3-849E0A9546CE}|GamalCompany.Data\\GamalCompany.Data.csproj|d:\\aiprojecttest\\sourcs\\gamalcompanyapp\\gamalcomapnyapp\\gamalcompany.data\\models\\treasurytransaction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2629204D-6E1D-48F6-BBD3-849E0A9546CE}|GamalCompany.Data\\GamalCompany.Data.csproj|solutionrelative:gamalcompany.data\\models\\treasurytransaction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|d:\\aiprojecttest\\sourcs\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapany.service\\mapping\\mappingprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|solutionrelative:gamalcomapany.service\\mapping\\mappingprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 4, "Children": [{"$type": "Document", "DocumentIndex": 2, "Title": "FinancialService.cs", "DocumentMoniker": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Implementations\\FinancialService.cs", "RelativeDocumentMoniker": "GamalComapany.Service\\Repositories\\Implementations\\FinancialService.cs", "ToolTip": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Implementations\\FinancialService.cs", "RelativeToolTip": "GamalComapany.Service\\Repositories\\Implementations\\FinancialService.cs", "ViewState": "AgIAACMCAAAAAAAAAAAAACMCAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T11:41:17.343Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "FinancialTransaction.cs", "DocumentMoniker": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalCompany.Data\\Models\\FinancialTransaction.cs", "RelativeDocumentMoniker": "GamalCompany.Data\\Models\\FinancialTransaction.cs", "ToolTip": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalCompany.Data\\Models\\FinancialTransaction.cs", "RelativeToolTip": "GamalCompany.Data\\Models\\FinancialTransaction.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAABMAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:56:27.726Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "RefranseFinancicalEnum.cs", "DocumentMoniker": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalCompany.Data\\Enum\\RefranseFinancicalEnum.cs", "RelativeDocumentMoniker": "GamalCompany.Data\\Enum\\RefranseFinancicalEnum.cs", "ToolTip": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalCompany.Data\\Enum\\RefranseFinancicalEnum.cs", "RelativeToolTip": "GamalCompany.Data\\Enum\\RefranseFinancicalEnum.cs", "ViewState": "AgIAAAEAAAAAAAAAAAAAABAAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:29:41.864Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "PartnerService.cs", "DocumentMoniker": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Implementations\\PartnerService.cs", "RelativeDocumentMoniker": "GamalComapany.Service\\Repositories\\Implementations\\PartnerService.cs", "ToolTip": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Implementations\\PartnerService.cs", "RelativeToolTip": "GamalComapany.Service\\Repositories\\Implementations\\PartnerService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:26:47.854Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "ApplicationDbContext.cs", "DocumentMoniker": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalCompany.Data\\Context\\ApplicationDbContext.cs", "RelativeDocumentMoniker": "GamalCompany.Data\\Context\\ApplicationDbContext.cs", "ToolTip": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalCompany.Data\\Context\\ApplicationDbContext.cs", "RelativeToolTip": "GamalCompany.Data\\Context\\ApplicationDbContext.cs", "ViewState": "AgIAAKcAAAAAAAAAAAApwLUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:23:27.944Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "IFinancialRepository.cs", "DocumentMoniker": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Interfaces\\IFinancialRepository.cs", "RelativeDocumentMoniker": "GamalComapany.Service\\Repositories\\Interfaces\\IFinancialRepository.cs", "ToolTip": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Repositories\\Interfaces\\IFinancialRepository.cs", "RelativeToolTip": "GamalComapany.Service\\Repositories\\Interfaces\\IFinancialRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T13:09:28.782Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "FinancialController.cs", "DocumentMoniker": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\Controllers\\FinancialController.cs", "RelativeDocumentMoniker": "GamalComapnyApp\\Controllers\\FinancialController.cs", "ToolTip": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\Controllers\\FinancialController.cs", "RelativeToolTip": "GamalComapnyApp\\Controllers\\FinancialController.cs", "ViewState": "AgIAAH0AAAAAAAAAAAAkwIwAAABPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T11:04:16.202Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "TreasuryTransaction.cs", "DocumentMoniker": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalCompany.Data\\Models\\TreasuryTransaction.cs", "RelativeDocumentMoniker": "GamalCompany.Data\\Models\\TreasuryTransaction.cs", "ToolTip": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalCompany.Data\\Models\\TreasuryTransaction.cs", "RelativeToolTip": "GamalCompany.Data\\Models\\TreasuryTransaction.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAAABUAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T10:22:04.278Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "MappingProfile.cs", "DocumentMoniker": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Mapping\\MappingProfile.cs", "RelativeDocumentMoniker": "GamalComapany.Service\\Mapping\\MappingProfile.cs", "ToolTip": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Mapping\\MappingProfile.cs", "RelativeToolTip": "GamalComapany.Service\\Mapping\\MappingProfile.cs", "ViewState": "AgIAACgAAAAAAAAAAAAQwEAAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T11:40:30.555Z", "EditorCaption": ""}]}]}]}