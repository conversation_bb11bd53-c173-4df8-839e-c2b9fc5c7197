using System.ComponentModel.DataAnnotations;

namespace GamalComapany.Service.Dtos.PartnerDto
{
    public class CreatePartnerDto
    {
        [Required(ErrorMessage = "اسم الشريك مطلوب")]
        [StringLength(100, ErrorMessage = "اسم الشريك يجب أن يكون أقل من 100 حرف")]
        public string NameEn { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string Description { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاستثمار الأولي مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "الاستثمار الأولي يجب أن يكون أكبر من صفر")]
        public decimal InitialInvestment { get; set; }

        [Range(0.01, 100, ErrorMessage = "نسبة المشاركة يجب أن تكون بين 0.01 و 100")]
        public decimal? SharePercentage { get; set; }
    }

    public class UpdatePartnerDto : CreatePartnerDto
    {
        [Required(ErrorMessage = "معرف الشريك مطلوب")]
        public int Id { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int? UpdatedBy { get; set; }
    }

    public class PartnerResponseDto
    {
        public int Id { get; set; }
        public string NameEn { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal? InitialInvestment { get; set; }
        public decimal? SharePercentage { get; set; }
        public decimal CurrentCapital { get; set; }
        public decimal TotalInvestments { get; set; }
        public decimal TotalWithdrawals { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public List<PartnerTransactionResponseDto> Transactions { get; set; } = new();
    }

    public class PartnerTransactionDto
    {
        [Required(ErrorMessage = "معرف الشريك مطلوب")]
        public int PartnerId { get; set; }

        [Required(ErrorMessage = "تاريخ المعاملة مطلوب")]
        public DateTime TransactionDate { get; set; }

        [Required(ErrorMessage = "نوع المعاملة مطلوب")]
        public int ActionDetailId { get; set; }

        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        public decimal Amount { get; set; }

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        [StringLength(1000, ErrorMessage = "الملاحظات يجب أن تكون أقل من 1000 حرف")]
        public string? Notes { get; set; }

        public string? ImagePath { get; set; }
    }

    public class PartnerTransactionResponseDto
    {
        public int Id { get; set; }
        public DateTime TransactionDate { get; set; }
        public int ActionDetailId { get; set; }
        public string ActionDetailName { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string? Description { get; set; }
        public string? Notes { get; set; }
        public string? ImagePath { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class PartnerCapitalCalculationDto
    {
        public int PartnerId { get; set; }
        public string PartnerName { get; set; } = string.Empty;
        public decimal InitialInvestment { get; set; }
        public decimal TotalAdditionalInvestments { get; set; }
        public decimal TotalWithdrawals { get; set; }
        public decimal CurrentCapital { get; set; }
        public decimal SharePercentage { get; set; }
        public decimal CalculatedSharePercentage { get; set; }
        public bool RequiresAdjustment { get; set; }
    }

    public class PartnerProfitDistributionDto
    {
        public int PartnerId { get; set; }
        public string PartnerName { get; set; } = string.Empty;
        public decimal SharePercentage { get; set; }
        public decimal ProfitShare { get; set; }
        public decimal TotalProfit { get; set; }
        public DateTime CalculationDate { get; set; }
    }

    /// <summary>
    /// DTO for creating partner transaction with treasury integration
    /// </summary>
    public class PartnerTransactionWithTreasuryDto
    {
        [Required(ErrorMessage = "معرف الشريك مطلوب")]
        public int PartnerId { get; set; }

        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        public decimal Amount { get; set; }

        [Required(ErrorMessage = "تاريخ المعاملة مطلوب")]
        public DateTime TransactionDate { get; set; }

        [Required(ErrorMessage = "نوع المعاملة مطلوب")]
        public int ActionDetailId { get; set; } // 32 = Investment, 33 = Withdrawal

        [Required(ErrorMessage = "معرف الخزينة مطلوب")]
        public int TreasuryId { get; set; }

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        [StringLength(1000, ErrorMessage = "الملاحظات يجب أن تكون أقل من 1000 حرف")]
        public string? Notes { get; set; }

        public string? ImagePath { get; set; }
    }

    /// <summary>
    /// Response DTO for partner transaction with treasury integration
    /// </summary>
    public class PartnerTransactionWithTreasuryResponseDto
    {
        public PartnerTransactionResponseDto PartnerTransaction { get; set; } = null!;
        public int FinancialTransactionId { get; set; }
        public int? TreasuryTransactionId { get; set; }
        public decimal NewPartnerCapital { get; set; }
        public decimal NewSharePercentage { get; set; }
        public decimal TreasuryBalance { get; set; }
    }

    /// <summary>
    /// DTO for profit distribution with treasury integration
    /// </summary>
    public class DistributeProfitDto
    {
        [Required(ErrorMessage = "إجمالي الربح مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "إجمالي الربح يجب أن يكون أكبر من صفر")]
        public decimal TotalProfit { get; set; }

        [Required(ErrorMessage = "تاريخ التوزيع مطلوب")]
        public DateTime DistributionDate { get; set; }

        [Required(ErrorMessage = "الوصف مطلوب")]
        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string Description { get; set; } = string.Empty;

        public int? TreasuryId { get; set; }
    }
}
