using AutoMapper;
using GamalComapany.Service.Dtos;
using GamalComapany.Service.Dtos.InventoryDto;
using GamalComapany.Service.Repositories.Interfaces;
using GamalComapany.Service.Services;
using GamalComapny.Service.Context;
using GamalCompany.Data.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace GamalComapany.Service.Repositories.Implementations
{
    public class InventoryService : ResponseHandler, IInventoryRepository
    {
        private readonly IUnitOfWorkOfService _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<InventoryService> _logger;
        private readonly IUserContext _userContext;
        private readonly IImageService _imageService;

        public InventoryService(
            IUnitOfWorkOfService unitOfWork, 
            IMapper mapper, 
            ILogger<InventoryService> logger,
            IUserContext userContext,
            IImageService imageService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
            _userContext = userContext;
            _imageService = imageService;
        }

        #region Inventory Transactions

        public async Task<ApiResponse<List<InventoryTransactionResponseDto>>> GetInventoryTransactionsAsync(int? itemId = null, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var query = _unitOfWork.InventoryTransactions.GetTableNoTracking()
                    .Include(it => it.Items)
                    .Include(it => it.Units)
                    .Include(it => it.MainAction)
                    .Where(it => !it.IsDeleted);

                if (itemId.HasValue)
                    query = query.Where(it => it.ItemId == itemId.Value);

                if (fromDate.HasValue)
                    query = query.Where(it => it.DateTransaction >= fromDate.Value);

                if (toDate.HasValue)
                    query = query.Where(it => it.DateTransaction <= toDate.Value);

                var transactions = await query
                    .OrderByDescending(it => it.DateTransaction)
                    .ToListAsync();

                var transactionDtos = _mapper.Map<List<InventoryTransactionResponseDto>>(transactions);
                return Success(transactionDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory transactions");
                return BadRequest<List<InventoryTransactionResponseDto>>("حدث خطأ أثناء جلب معاملات المخزون");
            }
        }

        public async Task<ApiResponse<InventoryTransactionResponseDto>> CreateInventoryTransactionAsync(CreateInventoryTransactionDto transactionDto)
        {
            try
            {
                // Validate item exists
                var item = await _unitOfWork.Items.GetByIdAsync(transactionDto.ItemId);
                if (item == null || item.IsDeleted)
                    return NotFound<InventoryTransactionResponseDto>("الصنف غير موجود");

                // Validate unit exists
                var unit = await _unitOfWork.Units.GetByIdAsync(transactionDto.UnitId);
                if (unit == null || unit.IsDeleted)
                    return NotFound<InventoryTransactionResponseDto>("الوحدة غير موجودة");

                // Validate action type exists
                var actionType = await _unitOfWork.ActionTypes.GetByIdAsync(transactionDto.ActionTypeId);
                if (actionType == null || actionType.IsDeleted)
                    return NotFound<InventoryTransactionResponseDto>("نوع العملية غير موجود");

                var transaction = _mapper.Map<InventoryTransaction>(transactionDto);
                // CreatedAt and CreatedBy will be set automatically by DbContext audit trail

                await _unitOfWork.InventoryTransactions.AddAsync(transaction);
                await _unitOfWork.SaveChangesAsync();

                // Get the created transaction with includes
                var createdTransaction = await _unitOfWork.InventoryTransactions.GetTableNoTracking()
                    .Include(it => it.Items)
                    .Include(it => it.Units)
                    .Include(it => it.MainAction)
                    .FirstOrDefaultAsync(it => it.Id == transaction.Id);

                var result = _mapper.Map<InventoryTransactionResponseDto>(createdTransaction);
                
                _logger.LogInformation("Inventory transaction created: {TransactionId} by user {UserId}", 
                    transaction.Id, _userContext.UserId);

                return Success(result, "تم إنشاء معاملة المخزون بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating inventory transaction");
                return BadRequest<InventoryTransactionResponseDto>("حدث خطأ أثناء إنشاء معاملة المخزون");
            }
        }

        public async Task<ApiResponse<bool>> DeleteInventoryTransactionAsync(int transactionId)
        {
            try
            {
                var transaction = await _unitOfWork.InventoryTransactions.GetByIdAsync(transactionId);
                if (transaction == null || transaction.IsDeleted)
                    return NotFound<bool>("معاملة المخزون غير موجودة");

                transaction.IsDeleted = true;
                // UpdatedAt and UpdatedBy will be set automatically by DbContext audit trail

                await _unitOfWork.InventoryTransactions.UpdateAsync(transaction);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Inventory transaction deleted: {TransactionId} by user {UserId}", 
                    transactionId, _userContext.UserId);

                return Success(true, "تم حذف معاملة المخزون بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting inventory transaction: {TransactionId}", transactionId);
                return BadRequest<bool>("حدث خطأ أثناء حذف معاملة المخزون");
            }
        }

        #endregion

        #region Stock Management

        public async Task<ApiResponse<List<ItemStockDto>>> GetItemsStockAsync()
        {
            try
            {
                var items = await _unitOfWork.Items.GetTableNoTracking()
                    .Include(i => i.ItemCategory)
                    .Include(i => i.ItemUnit)
                    .Where(i => !i.IsDeleted)
                    .ToListAsync();

                var stockDtos = new List<ItemStockDto>();

                foreach (var item in items)
                {
                    var currentStock = await CalculateCurrentStockAsync(item.Id);
                    var lastTransaction = await GetLastTransactionDateAsync(item.Id);

                    var stockDto = new ItemStockDto
                    {
                        ItemId = item.Id,
                        ItemName = item.NameEn,
                        ItemCode = item.ItemCode ?? "",
                        CategoryName = item.ItemCategory?.NameEn ?? "",
                        UnitName = item.ItemUnit?.NameEn ?? "",
                        CurrentStock = currentStock,
                        MinimumStock = item.MinimumStock,
                        MaximumStock = item.MaximumStock,
                        ReorderLevel = item.ReorderLevel,
                        StandardCost = item.StandardCost,
                        TotalValue = currentStock * item.StandardCost,
                        IsLowStock = item.MinimumStock.HasValue && currentStock <= item.MinimumStock.Value,
                        LastTransactionDate = lastTransaction
                    };

                    stockDtos.Add(stockDto);
                }

                return Success(stockDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting items stock");
                return BadRequest<List<ItemStockDto>>("حدث خطأ أثناء جلب مخزون الأصناف");
            }
        }

        public async Task<ApiResponse<ItemStockDto>> GetItemStockAsync(int itemId)
        {
            try
            {
                var item = await _unitOfWork.Items.GetTableNoTracking()
                    .Include(i => i.ItemCategory)
                    .Include(i => i.ItemUnit)
                    .FirstOrDefaultAsync(i => i.Id == itemId && !i.IsDeleted);

                if (item == null)
                    return NotFound<ItemStockDto>("الصنف غير موجود");

                var currentStock = await CalculateCurrentStockAsync(itemId);
                var lastTransaction = await GetLastTransactionDateAsync(itemId);

                var stockDto = new ItemStockDto
                {
                    ItemId = item.Id,
                    ItemName = item.NameEn,
                    ItemCode = item.ItemCode ?? "",
                    CategoryName = item.ItemCategory?.NameEn ?? "",
                    UnitName = item.ItemUnit?.NameEn ?? "",
                    CurrentStock = currentStock,
                    MinimumStock = item.MinimumStock,
                    MaximumStock = item.MaximumStock,
                    ReorderLevel = item.ReorderLevel,
                    StandardCost = item.StandardCost,
                    TotalValue = currentStock * item.StandardCost,
                    IsLowStock = item.MinimumStock.HasValue && currentStock <= item.MinimumStock.Value,
                    LastTransactionDate = lastTransaction
                };

                return Success(stockDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item stock: {ItemId}", itemId);
                return BadRequest<ItemStockDto>("حدث خطأ أثناء جلب مخزون الصنف");
            }
        }

        public async Task<ApiResponse<List<ItemStockDto>>> GetLowStockItemsAsync()
        {
            try
            {
                var allStock = await GetItemsStockAsync();
                if (!allStock.Succeeded)
                    return allStock;

                var lowStockItems = allStock.Data.Where(s => s.IsLowStock).ToList();
                return Success(lowStockItems);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting low stock items");
                return BadRequest<List<ItemStockDto>>("حدث خطأ أثناء جلب الأصناف منخفضة المخزون");
            }
        }

        #endregion

        #region Helper Methods

        private async Task<decimal> CalculateCurrentStockAsync(int itemId)
        {
            try
            {
                var transactions = await _unitOfWork.InventoryTransactions.GetTableNoTracking()
                    .Include(it => it.MainAction)
                    .Where(it => it.ItemId == itemId && !it.IsDeleted)
                    .ToListAsync();

                decimal currentStock = 0;

                foreach (var transaction in transactions)
                {
                    // Assuming ActionTypeId 1 = In, 2 = Out (this should be configurable)
                    if (transaction.ActionTypeId == 1) // Stock In
                    {
                        currentStock += transaction.Quantity;
                    }
                    else if (transaction.ActionTypeId == 2) // Stock Out
                    {
                        currentStock -= transaction.Quantity;
                    }
                }

                return currentStock;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating current stock for item: {ItemId}", itemId);
                return 0;
            }
        }

        private async Task<DateTime> GetLastTransactionDateAsync(int itemId)
        {
            try
            {
                var lastTransaction = await _unitOfWork.InventoryTransactions.GetTableNoTracking()
                    .Where(it => it.ItemId == itemId && !it.IsDeleted)
                    .OrderByDescending(it => it.DateTransaction)
                    .FirstOrDefaultAsync();

                return lastTransaction?.DateTransaction ?? DateTime.MinValue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting last transaction date for item: {ItemId}", itemId);
                return DateTime.MinValue;
            }
        }

        #endregion

        #region Inventory Movement Reports

        public async Task<ApiResponse<InventoryMovementDto>> GetInventoryMovementAsync(int itemId, DateTime fromDate, DateTime toDate)
        {
            try
            {
                var item = await _unitOfWork.Items.GetTableNoTracking()
                    .FirstOrDefaultAsync(i => i.Id == itemId && !i.IsDeleted);

                if (item == null)
                    return NotFound<InventoryMovementDto>("الصنف غير موجود");

                // Get opening balance (stock before fromDate)
                var openingTransactions = await _unitOfWork.InventoryTransactions.GetTableNoTracking()
                    .Where(it => it.ItemId == itemId && it.DateTransaction < fromDate && !it.IsDeleted)
                    .ToListAsync();

                var openingBalance = CalculateStockFromTransactions(openingTransactions);

                // Get transactions in the period
                var periodTransactions = await _unitOfWork.InventoryTransactions.GetTableNoTracking()
                    .Include(it => it.Items)
                    .Include(it => it.Units)
                    .Include(it => it.MainAction)
                    .Where(it => it.ItemId == itemId &&
                                it.DateTransaction >= fromDate &&
                                it.DateTransaction <= toDate &&
                                !it.IsDeleted)
                    .OrderBy(it => it.DateTransaction)
                    .ToListAsync();

                var totalIn = periodTransactions.Where(t => t.ActionTypeId == 1).Sum(t => t.Quantity);
                var totalOut = periodTransactions.Where(t => t.ActionTypeId == 2).Sum(t => t.Quantity);
                var closingBalance = openingBalance + totalIn - totalOut;

                var movementDto = new InventoryMovementDto
                {
                    ItemId = itemId,
                    ItemName = item.NameEn,
                    FromDate = fromDate,
                    ToDate = toDate,
                    OpeningBalance = openingBalance,
                    TotalIn = totalIn,
                    TotalOut = totalOut,
                    ClosingBalance = closingBalance,
                    Transactions = _mapper.Map<List<InventoryTransactionResponseDto>>(periodTransactions)
                };

                return Success(movementDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory movement for item: {ItemId}", itemId);
                return BadRequest<InventoryMovementDto>("حدث خطأ أثناء جلب حركة المخزون");
            }
        }

        public async Task<ApiResponse<List<InventoryMovementDto>>> GetInventoryMovementReportAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var items = await _unitOfWork.Items.GetTableNoTracking()
                    .Where(i => !i.IsDeleted)
                    .ToListAsync();

                var movements = new List<InventoryMovementDto>();

                foreach (var item in items)
                {
                    var movement = await GetInventoryMovementAsync(item.Id, fromDate, toDate);
                    if (movement.Succeeded)
                    {
                        movements.Add(movement.Data);
                    }
                }

                return Success(movements);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inventory movement report");
                return BadRequest<List<InventoryMovementDto>>("حدث خطأ أثناء جلب تقرير حركة المخزون");
            }
        }

        #endregion

        #region Item Images

        public async Task<ApiResponse<List<ItemImageResponseDto>>> GetItemImagesAsync(int itemId)
        {
            try
            {
                var images = await _unitOfWork.ItemImages.GetTableNoTracking()
                    .Where(ii => ii.ItemId == itemId && !ii.IsDeleted)
                    .OrderByDescending(ii => ii.IsPrimary)
                    .ThenBy(ii => ii.CreatedAt)
                    .ToListAsync();

                var imageDtos = _mapper.Map<List<ItemImageResponseDto>>(images);
                return Success(imageDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item images: {ItemId}", itemId);
                return BadRequest<List<ItemImageResponseDto>>("حدث خطأ أثناء جلب صور الصنف");
            }
        }

        public async Task<ApiResponse<ItemImageResponseDto>> CreateItemImageAsync(CreateItemImageDto imageDto)
        {
            try
            {
                var item = await _unitOfWork.Items.GetByIdAsync(imageDto.ItemId);
                if (item == null || item.IsDeleted)
                    return NotFound<ItemImageResponseDto>("الصنف غير موجود");

                var itemImage = _mapper.Map<ItemImage>(imageDto);
                // CreatedAt and CreatedBy will be set automatically by DbContext audit trail

                // If this is set as main image, unset other main images
                if (imageDto.IsPrimary)
                {
                    var existingMainImages = await _unitOfWork.ItemImages.GetTableAsTracking()
                        .Where(ii => ii.ItemId == imageDto.ItemId && ii.IsPrimary && !ii.IsDeleted)
                        .ToListAsync();

                    foreach (var existingImage in existingMainImages)
                    {
                        existingImage.IsPrimary = false;
                        // UpdatedAt and UpdatedBy will be set automatically by DbContext audit trail
                    }

                    await _unitOfWork.ItemImages.UpdateRangeAsync(existingMainImages);
                }

                await _unitOfWork.ItemImages.AddAsync(itemImage);
                await _unitOfWork.SaveChangesAsync();

                var result = _mapper.Map<ItemImageResponseDto>(itemImage);

                _logger.LogInformation("Item image created: {ImageId} for item {ItemId} by user {UserId}",
                    itemImage.Id, imageDto.ItemId, _userContext.UserId);

                return Success(result, "تم إضافة صورة الصنف بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating item image");
                return BadRequest<ItemImageResponseDto>("حدث خطأ أثناء إضافة صورة الصنف");
            }
        }

        public async Task<ApiResponse<bool>> DeleteItemImageAsync(int imageId)
        {
            try
            {
                var itemImage = await _unitOfWork.ItemImages.GetByIdAsync(imageId);
                if (itemImage == null || itemImage.IsDeleted)
                    return NotFound<bool>("صورة الصنف غير موجودة");

                // Delete physical file
                await _imageService.DeleteItemImageAsync(itemImage.ImageUrl ?? "");

                itemImage.IsDeleted = true;
                itemImage.UpdatedAt = DateTime.Now;
                itemImage.UpdatedBy = _userContext.UserId;

                await _unitOfWork.ItemImages.UpdateAsync(itemImage);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Item image deleted: {ImageId} by user {UserId}",
                    imageId, _userContext.UserId);

                return Success(true, "تم حذف صورة الصنف بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting item image: {ImageId}", imageId);
                return BadRequest<bool>("حدث خطأ أثناء حذف صورة الصنف");
            }
        }

        public async Task<ApiResponse<bool>> SetMainImageAsync(int itemId, int imageId)
        {
            try
            {
                var itemImage = await _unitOfWork.ItemImages.GetByIdAsync(imageId);
                if (itemImage == null || itemImage.IsDeleted || itemImage.ItemId != itemId)
                    return NotFound<bool>("صورة الصنف غير موجودة");

                // Unset other main images
                var existingMainImages = await _unitOfWork.ItemImages.GetTableAsTracking()
                    .Where(ii => ii.ItemId == itemId && ii.IsPrimary && !ii.IsDeleted)
                    .ToListAsync();

                foreach (var existingImage in existingMainImages)
                {
                    existingImage.IsPrimary = false;
                    existingImage.UpdatedAt = DateTime.Now;
                    existingImage.UpdatedBy = _userContext.UserId;
                }

                await _unitOfWork.ItemImages.UpdateRangeAsync(existingMainImages);

                // Set new main image
                itemImage.IsPrimary = true;
                itemImage.UpdatedAt = DateTime.Now;
                itemImage.UpdatedBy = _userContext.UserId;

                await _unitOfWork.ItemImages.UpdateAsync(itemImage);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Main image set: {ImageId} for item {ItemId} by user {UserId}",
                    imageId, itemId, _userContext.UserId);

                return Success(true, "تم تعيين الصورة الرئيسية بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting main image: {ImageId}", imageId);
                return BadRequest<bool>("حدث خطأ أثناء تعيين الصورة الرئيسية");
            }
        }

        #endregion

        #region Item Management Extensions

        public async Task<ApiResponse<ItemResponseDto>> UpdateItemAsync(UpdateItemDto updateItemDto)
        {
            try
            {
                var existingItem = await _unitOfWork.Items.GetByIdAsync(updateItemDto.Id);
                if (existingItem == null || existingItem.IsDeleted)
                    return NotFound<ItemResponseDto>("الصنف غير موجود");

                // Check if item code is unique (if provided)
                if (!string.IsNullOrEmpty(updateItemDto.ItemCode))
                {
                    var duplicateItem = await _unitOfWork.Items.GetTableNoTracking()
                        .FirstOrDefaultAsync(i => i.ItemCode == updateItemDto.ItemCode && i.Id != updateItemDto.Id && !i.IsDeleted);

                    if (duplicateItem != null)
                        return BadRequest<ItemResponseDto>("كود الصنف موجود بالفعل");
                }

                _mapper.Map(updateItemDto, existingItem);
                existingItem.UpdatedAt = DateTime.Now;
                existingItem.UpdatedBy = _userContext.UserId;

                await _unitOfWork.Items.UpdateAsync(existingItem);
                await _unitOfWork.SaveChangesAsync();

                // Get updated item with includes
                var updatedItem = await _unitOfWork.Items.GetTableNoTracking()
                    .Include(i => i.ItemCategory)
                    .Include(i => i.ItemUnit)
                    .Include(i => i.ItemImages)
                    .FirstOrDefaultAsync(i => i.Id == existingItem.Id);

                var result = _mapper.Map<ItemResponseDto>(updatedItem);

                _logger.LogInformation("Item updated: {ItemId} by user {UserId}",
                    existingItem.Id, _userContext.UserId);

                return Success(result, "تم تحديث الصنف بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating item: {ItemId}", updateItemDto.Id);
                return BadRequest<ItemResponseDto>("حدث خطأ أثناء تحديث الصنف");
            }
        }

        public async Task<ApiResponse<List<ItemResponseDto>>> GetItemsByCategoryAsync(int categoryId)
        {
            try
            {
                var items = await _unitOfWork.Items.GetTableNoTracking()
                    .Include(i => i.ItemCategory)
                    .Include(i => i.ItemUnit)
                    .Include(i => i.ItemImages)
                    .Where(i => i.CategoryId == categoryId && !i.IsDeleted)
                    .ToListAsync();

                var itemDtos = _mapper.Map<List<ItemResponseDto>>(items);
                return Success(itemDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting items by category: {CategoryId}", categoryId);
                return BadRequest<List<ItemResponseDto>>("حدث خطأ أثناء جلب أصناف التصنيف");
            }
        }

        public async Task<ApiResponse<List<ItemResponseDto>>> SearchItemsAsync(string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return BadRequest<List<ItemResponseDto>>("مصطلح البحث مطلوب");

                var items = await _unitOfWork.Items.GetTableNoTracking()
                    .Include(i => i.ItemCategory)
                    .Include(i => i.ItemUnit)
                    .Include(i => i.ItemImages)
                    .Where(i => !i.IsDeleted &&
                               (i.NameEn.Contains(searchTerm) ||
                                (i.NameAr != null && i.NameAr.Contains(searchTerm)) ||
                                (i.ItemCode != null && i.ItemCode.Contains(searchTerm)) ||
                                (i.Barcode != null && i.Barcode.Contains(searchTerm))))
                    .ToListAsync();

                var itemDtos = _mapper.Map<List<ItemResponseDto>>(items);
                return Success(itemDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching items with term: {SearchTerm}", searchTerm);
                return BadRequest<List<ItemResponseDto>>("حدث خطأ أثناء البحث في الأصناف");
            }
        }

        #endregion

        #region Helper Methods

        private decimal CalculateStockFromTransactions(List<InventoryTransaction> transactions)
        {
            decimal stock = 0;
            foreach (var transaction in transactions)
            {
                if (transaction.ActionTypeId == 1) // Stock In
                {
                    stock += transaction.Quantity;
                }
                else if (transaction.ActionTypeId == 2) // Stock Out
                {
                    stock -= transaction.Quantity;
                }
            }
            return stock;
        }

        #endregion
    }
}
