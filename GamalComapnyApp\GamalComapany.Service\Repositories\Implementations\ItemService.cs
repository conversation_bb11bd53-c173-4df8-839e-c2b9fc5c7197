﻿using AutoMapper;
using GamalComapany.Service.Dtos;
using GamalComapany.Service.Dtos.ItemDto;
using GamalComapany.Service.Dtos.InventoryDto;
using GamalComapany.Service.Repositories.Interfaces;
using GamalComapny.Service.Context;
using GamalCompany.Data.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace GamalComapany.Service.Repositories.Implementations
{
    public class ItemService : ResponseHandler, IItemRepository
    {
        private readonly IUnitOfWorkOfService _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<ItemService> _logger;
        private readonly IUserContext _userContext;

        public ItemService(IUnitOfWorkOfService unitOfWork, IMapper mapper, ILogger<ItemService> logger, IUserContext userContext)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
            _userContext = userContext;
        }

        public async Task<ApiResponse<List<ItemDto>>> GetItems()
        {
            try
            {
                var items = await _unitOfWork.Items.GetTableNoTracking()
                    .Include(i => i.ItemCategory)
                    .Include(i => i.ItemUnit)
                    .Include(i => i.ItemImages)
                    .Where(i => !i.IsDeleted)
                    .ToListAsync();

                if (!items.Any())
                    return NotFound<List<ItemDto>>("لا توجد أصناف");

                var itemDtos = _mapper.Map<List<ItemDto>>(items);
                return Success(itemDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting items");
                return BadRequest<List<ItemDto>>("حدث خطأ أثناء جلب الأصناف");
            }
        }

        public async Task<ApiResponse<ItemDto>> GetItemById(int id)
        {
            try
            {
                var item = await _unitOfWork.Items.GetTableNoTracking()
                    .Include(i => i.ItemCategory)
                    .Include(i => i.ItemUnit)
                    .Include(i => i.ItemImages)
                    .FirstOrDefaultAsync(i => i.Id == id && !i.IsDeleted);

                if (item == null)
                    return NotFound<ItemDto>("الصنف غير موجود");

                var itemDto = _mapper.Map<ItemDto>(item);
                return Success(itemDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item by id: {ItemId}", id);
                return BadRequest<ItemDto>("حدث خطأ أثناء جلب الصنف");
            }
        }

        public async Task<ApiResponse<ItemDto>> CreateItem(CreateItemDto createItemDto)
        {
            try
            {
                // Check if item code is unique (if provided)
                if (!string.IsNullOrEmpty(createItemDto.ItemCode))
                {
                    var existingItem = await _unitOfWork.Items.GetTableNoTracking()
                        .FirstOrDefaultAsync(i => i.ItemCode == createItemDto.ItemCode && !i.IsDeleted);

                    if (existingItem != null)
                        return BadRequest<ItemDto>("كود الصنف موجود بالفعل");
                }

                var item = _mapper.Map<Item>(createItemDto);
                // CreatedAt and CreatedBy will be set automatically by DbContext audit trail

                await _unitOfWork.Items.AddAsync(item);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Item created: {ItemId} by user {UserId}", item.Id, _userContext.UserId);

                var result = await GetItemById(item.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating item");
                return BadRequest<ItemDto>("حدث خطأ أثناء إنشاء الصنف");
            }
        }

        public async Task<ApiResponse<ItemDto>> UpdateItem(Dtos.ItemDto.UpdateItemItemDto updateItemDto)
        {
            try
            {
                var existingItem = await _unitOfWork.Items.GetByIdAsync(updateItemDto.Id);
                if (existingItem == null || existingItem.IsDeleted)
                    return NotFound<ItemDto>("الصنف غير موجود");

                // Check if item code is unique (if provided)
                if (!string.IsNullOrEmpty(updateItemDto.ItemCode))
                {
                    var duplicateItem = await _unitOfWork.Items.GetTableNoTracking()
                        .FirstOrDefaultAsync(i => i.ItemCode == updateItemDto.ItemCode && i.Id != updateItemDto.Id && !i.IsDeleted);

                    if (duplicateItem != null)
                        return BadRequest<ItemDto>("كود الصنف موجود بالفعل");
                }

                _mapper.Map(updateItemDto, existingItem);
                // UpdatedAt and UpdatedBy will be set automatically by DbContext audit trail

                await _unitOfWork.Items.UpdateAsync(existingItem);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Item updated: {ItemId} by user {UserId}", existingItem.Id, _userContext.UserId);

                var result = await GetItemById(existingItem.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating item: {ItemId}", updateItemDto.Id);
                return BadRequest<ItemDto>("حدث خطأ أثناء تحديث الصنف");
            }
        }

        public async Task<ApiResponse<bool>> DeleteItem(int id)
        {
            try
            {
                var item = await _unitOfWork.Items.GetByIdAsync(id);
                if (item == null || item.IsDeleted)
                    return NotFound<bool>("الصنف غير موجود");

                // Check if item has inventory transactions
                var hasTransactions = await _unitOfWork.InventoryTransactions.GetTableNoTracking()
                    .AnyAsync(it => it.ItemId == id && !it.IsDeleted);

                if (hasTransactions)
                    return BadRequest<bool>("لا يمكن حذف الصنف لوجود معاملات مخزون مرتبطة به");

                item.IsDeleted = true;
                // UpdatedAt and UpdatedBy will be set automatically by DbContext audit trail

                await _unitOfWork.Items.UpdateAsync(item);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Item deleted: {ItemId} by user {UserId}", id, _userContext.UserId);

                return Success(true, "تم حذف الصنف بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting item: {ItemId}", id);
                return BadRequest<bool>("حدث خطأ أثناء حذف الصنف");
            }
        }
    }
}
