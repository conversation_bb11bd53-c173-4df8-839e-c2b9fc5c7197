using GamalComapany.Service.Authorization;
using GamalComapany.Service.Dtos.UserDto;
using GamalComapany.Service.Repositories.Interfaces;
using GamalComapnyApp.API.Base;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace GamalComapnyApp.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UserController : AppControllerBase
    {
        public UserController(IUnitOfWork work) : base(work)
        {
        }

        #region Authentication Endpoints

        /// <summary>
        /// User login
        /// </summary>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login(LoginDto loginDto)
        {
            var response = await _work.UserRepository.LoginAsync(loginDto);
            return NewResult(response);
        }

        /// <summary>
        /// Refresh access token
        /// </summary>
        [HttpPost("refresh-token")]
        [AllowAnonymous]
        public async Task<IActionResult> RefreshToken(RefreshTokenDto refreshTokenDto)
        {
            var response = await _work.UserRepository.RefreshTokenAsync(refreshTokenDto);
            return NewResult(response);
        }

        /// <summary>
        /// User logout
        /// </summary>
        [HttpPost("logout")]
        [Authorize]
        public async Task<IActionResult> Logout()
        {
            var userId = int.Parse(User.FindFirst("UserId")?.Value ?? "0");
            var response = await _work.UserRepository.LogoutAsync(userId);
            return NewResult(response);
        }

        /// <summary>
        /// Validate token
        /// </summary>
        [HttpPost("validate-token")]
        [Authorize]
        public async Task<IActionResult> ValidateToken()
        {
            var token = Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
            var response = await _work.UserRepository.ValidateTokenAsync(token);
            return NewResult(response);
        }

        #endregion

        #region User Management

        /// <summary>
        /// Get all users
        /// </summary>
        [HttpGet]
        [RequireUserManagement]
        public async Task<IActionResult> GetAllUsers()
        {
            var response = await _work.UserRepository.GetAllUsersAsync();
            return NewResult(response);
        }

        /// <summary>
        /// Get user by ID
        /// </summary>
        [HttpGet("{id}")]
        [RequireUserManagement]
        public async Task<IActionResult> GetUserById(int id)
        {
            var response = await _work.UserRepository.GetUserByIdAsync(id);
            return NewResult(response);
        }

        /// <summary>
        /// Get user by username
        /// </summary>
        [HttpGet("username/{username}")]
        [RequireUserManagement]
        public async Task<IActionResult> GetUserByUsername(string username)
        {
            var response = await _work.UserRepository.GetUserByUsernameAsync(username);
            return NewResult(response);
        }

        /// <summary>
        /// Create new user
        /// </summary>
        [HttpPost]
        [RequireUserManagement]
        //[AllowAnonymous]
        public async Task<IActionResult> CreateUser(CreateUserDto userDto)
        {
            var response = await _work.UserRepository.CreateUserAsync(userDto);
            return NewResult(response);
        }

        /// <summary>
        /// Update user
        /// </summary>
        [HttpPut]
        [RequireUserManagement]
        public async Task<IActionResult> UpdateUser(UpdateUserDto userDto)
        {
            var response = await _work.UserRepository.UpdateUserAsync(userDto);
            return NewResult(response);
        }

        /// <summary>
        /// Delete user
        /// </summary>
        [HttpDelete("{id}")]
        [RequireSystemAdmin]
        public async Task<IActionResult> DeleteUser(int id)
        {
            var response = await _work.UserRepository.DeleteUserAsync(id);
            return NewResult(response);
        }

        /// <summary>
        /// Change password
        /// </summary>
        [HttpPost("change-password")]
        [Authorize]
        public async Task<IActionResult> ChangePassword(ChangePasswordDto changePasswordDto)
        {
            var response = await _work.UserRepository.ChangePasswordAsync(changePasswordDto);
            return NewResult(response);
        }

        /// <summary>
        /// Reset password (Admin only)
        /// </summary>
        [HttpPost("reset-password")]
        [RequireSystemAdmin]
        public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordRequest request)
        {
            var response = await _work.UserRepository.ResetPasswordAsync(request.UserId, request.NewPassword);
            return NewResult(response);
        }

        #endregion

        #region Permission Management

        /// <summary>
        /// Get user permissions
        /// </summary>
        [HttpGet("{userId}/permissions")]
        [RequireUserManagement]
        public async Task<IActionResult> GetUserPermissions(int userId)
        {
            var response = await _work.UserRepository.GetUserPermissionsAsync(userId);
            return NewResult(response);
        }

        /// <summary>
        /// Create user permission
        /// </summary>
        [HttpPost("permissions")]
        [RequireUserManagement]
        public async Task<IActionResult> CreateUserPermission(CreateUserPermissionDto permissionDto)
        {
            var response = await _work.UserRepository.CreateUserPermissionAsync(permissionDto);
            return NewResult(response);
        }

        /// <summary>
        /// Delete user permission
        /// </summary>
        [HttpDelete("permissions/{permissionId}")]
        [RequireUserManagement]
        public async Task<IActionResult> DeleteUserPermission(int permissionId)
        {
            var response = await _work.UserRepository.DeleteUserPermissionAsync(permissionId);
            return NewResult(response);
        }

        /// <summary>
        /// Get user permission summary
        /// </summary>
        [HttpGet("{userId}/permission-summary")]
        [RequireUserManagement]
        public async Task<IActionResult> GetUserPermissionSummary(int userId)
        {
            var response = await _work.UserRepository.GetUserPermissionSummaryAsync(userId);
            return NewResult(response);
        }

        /// <summary>
        /// Check if user has specific permission
        /// </summary>
        [HttpGet("{userId}/has-permission")]
        [RequireUserManagement]
        public async Task<IActionResult> HasPermission(int userId, [FromQuery] string module, [FromQuery] string permission)
        {
            var response = await _work.UserRepository.HasPermissionAsync(userId, module, permission);
            return NewResult(response);
        }

        #endregion

        #region Module Management

        /// <summary>
        /// Get all modules
        /// </summary>
        [HttpGet("modules")]
        [RequireUserManagement]
        public async Task<IActionResult> GetAllModules()
        {
            var response = await _work.UserRepository.GetAllModulesAsync();
            return NewResult(response);
        }

        /// <summary>
        /// Get module by ID
        /// </summary>
        [HttpGet("modules/{id}")]
        [RequireUserManagement]
        public async Task<IActionResult> GetModuleById(int id)
        {
            var response = await _work.UserRepository.GetModuleByIdAsync(id);
            return NewResult(response);
        }

        /// <summary>
        /// Create new module
        /// </summary>
        [HttpPost("modules")]
        [RequireSystemAdmin]
        public async Task<IActionResult> CreateModule(CreateModuleDto moduleDto)
        {
            var response = await _work.UserRepository.CreateModuleAsync(moduleDto);
            return NewResult(response);
        }

        /// <summary>
        /// Update module
        /// </summary>
        [HttpPut("modules")]
        [RequireSystemAdmin]
        public async Task<IActionResult> UpdateModule(UpdateModuleDto moduleDto)
        {
            var response = await _work.UserRepository.UpdateModuleAsync(moduleDto);
            return NewResult(response);
        }

        /// <summary>
        /// Delete module
        /// </summary>
        [HttpDelete("modules/{id}")]
        [RequireSystemAdmin]
        public async Task<IActionResult> DeleteModule(int id)
        {
            var response = await _work.UserRepository.DeleteModuleAsync(id);
            return NewResult(response);
        }

        #endregion

        #region Profile Management

        /// <summary>
        /// Get current user profile
        /// </summary>
        [HttpGet("profile")]
        [Authorize]
        public async Task<IActionResult> GetProfile()
        {
            var userId = int.Parse(User.FindFirst("UserId")?.Value ?? "0");
            var response = await _work.UserRepository.GetUserByIdAsync(userId);
            return NewResult(response);
        }

        /// <summary>
        /// Update current user profile
        /// </summary>
        [HttpPut("profile")]
        [Authorize]
        public async Task<IActionResult> UpdateProfile([FromForm] UpdateUserProfileDto updateDto)
        {
            var userId = int.Parse(User.FindFirst("UserId")?.Value ?? "0");
            updateDto.Id = userId;

            var response = await _work.UserRepository.UpdateUserProfileAsync(updateDto);
            return NewResult(response);
        }

        /// <summary>
        /// Upload profile image for current user
        /// </summary>
        [HttpPost("profile/upload-image")]
        [Authorize]
        public async Task<IActionResult> UploadProfileImage([FromForm] IFormFile profileImage)
        {
            var userId = int.Parse(User.FindFirst("UserId")?.Value ?? "0");
            var updateDto = new UpdateProfileImageDto
            {
                UserId = userId,
                ProfileImage = profileImage
            };

            var response = await _work.UserRepository.UpdateProfileImageAsync(updateDto);
            return NewResult(response);
        }

        /// <summary>
        /// Upload profile image for specific user (Admin only)
        /// </summary>
        [HttpPost("{userId}/upload-image")]
        [RequireUserManagement]
        public async Task<IActionResult> UploadUserProfileImage(int userId, [FromForm] IFormFile profileImage)
        {
            var updateDto = new UpdateProfileImageDto
            {
                UserId = userId,
                ProfileImage = profileImage
            };

            var response = await _work.UserRepository.UpdateProfileImageAsync(updateDto);
            return NewResult(response);
        }

        /// <summary>
        /// Delete profile image for current user
        /// </summary>
        [HttpDelete("profile/image")]
        [Authorize]
        public async Task<IActionResult> DeleteProfileImage()
        {
            var userId = int.Parse(User.FindFirst("UserId")?.Value ?? "0");
            var response = await _work.UserRepository.DeleteProfileImageAsync(userId);
            return NewResult(response);
        }

        /// <summary>
        /// Delete profile image for specific user (Admin only)
        /// </summary>
        [HttpDelete("{userId}/image")]
        [RequireUserManagement]
        public async Task<IActionResult> DeleteUserProfileImage(int userId)
        {
            var response = await _work.UserRepository.DeleteProfileImageAsync(userId);
            return NewResult(response);
        }

        #endregion
    }

    // Helper classes for request models
    public class ResetPasswordRequest
    {
        public int UserId { get; set; }
        public string NewPassword { get; set; } = string.Empty;
    }

    public class UpdateUserProfileRequest
    {
        public string UserName { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string ProfileImage { get; set; } = string.Empty;
    }
}
