using GamalComapany.Service.Authorization;
using GamalComapany.Service.Dtos.FinancialDto;
using GamalComapany.Service.Repositories.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace GamalComapnyApp.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class FinancialController : AppControllerBase
    {
        private readonly IFinancialRepository _financialRepository;

        public FinancialController(IFinancialRepository financialRepository)
        {
            _financialRepository = financialRepository;
        }

        #region Treasury Management

        /// <summary>
        /// Get all treasuries
        /// </summary>
        [HttpGet("treasuries")]
        [RequireFinancialRead]
        public async Task<IActionResult> GetAllTreasuries()
        {
            var response = await _financialRepository.GetAllTreasuriesAsync();
            return NewResult(response);
        }

        /// <summary>
        /// Get treasury by ID
        /// </summary>
        [HttpGet("treasuries/{id}")]
        [RequireFinancialRead]
        public async Task<IActionResult> GetTreasuryById(int id)
        {
            var response = await _financialRepository.GetTreasuryByIdAsync(id);
            return NewResult(response);
        }

        /// <summary>
        /// Create new treasury
        /// </summary>
        [HttpPost("treasuries")]
        [RequireFinancialWrite]
        public async Task<IActionResult> CreateTreasury(CreateTreasuryDto treasuryDto)
        {
            var response = await _financialRepository.CreateTreasuryAsync(treasuryDto);
            return NewResult(response);
        }

        /// <summary>
        /// Update treasury
        /// </summary>
        [HttpPut("treasuries")]
        [RequireFinancialWrite]
        public async Task<IActionResult> UpdateTreasury(UpdateTreasuryDto treasuryDto)
        {
            var response = await _financialRepository.UpdateTreasuryAsync(treasuryDto);
            return NewResult(response);
        }

        /// <summary>
        /// Delete treasury
        /// </summary>
        [HttpDelete("treasuries/{id}")]
        [RequireFinancialManage]
        public async Task<IActionResult> DeleteTreasury(int id)
        {
            var response = await _financialRepository.DeleteTreasuryAsync(id);
            return NewResult(response);
        }

        #endregion

        #region Treasury Transactions

        /// <summary>
        /// Get treasury transactions with optional filters
        /// </summary>
        [HttpGet("treasury-transactions")]
        [RequireFinancialRead]
        public async Task<IActionResult> GetTreasuryTransactions(
            [FromQuery] int? treasuryId = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            var response = await _financialRepository.GetTreasuryTransactionsAsync(treasuryId, fromDate, toDate);
            return NewResult(response);
        }

        /// <summary>
        /// Create treasury transaction
        /// </summary>
        [HttpPost("treasury-transactions")]
        [RequireFinancialWrite]
        public async Task<IActionResult> CreateTreasuryTransaction(CreateTreasuryTransactionDto transactionDto)
        {
            var response = await _financialRepository.CreateTreasuryTransactionAsync(transactionDto);
            return NewResult(response);
        }

        /// <summary>
        /// Delete treasury transaction
        /// </summary>
        [HttpDelete("treasury-transactions/{id}")]
        [RequireFinancialManage]
        public async Task<IActionResult> DeleteTreasuryTransaction(int id)
        {
            var response = await _financialRepository.DeleteTreasuryTransactionAsync(id);
            return NewResult(response);
        }

        #endregion

        #region Financial Transactions

        /// <summary>
        /// Get financial transactions with optional date filters
        /// </summary>
        [HttpGet("transactions")]
        [RequireFinancialRead]
        public async Task<IActionResult> GetFinancialTransactions(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            var response = await _financialRepository.GetFinancialTransactionsAsync(fromDate, toDate);
            return NewResult(response);
        }

        /// <summary>
        /// Create financial transaction
        /// </summary>
        [HttpPost("transactions")]
        [RequireFinancialWrite]
        public async Task<IActionResult> CreateFinancialTransaction(CreateFinancialTransactionDto transactionDto)
        {
            var response = await _financialRepository.CreateFinancialTransactionAsync(transactionDto);
            return NewResult(response);
        }

        /// <summary>
        /// Delete financial transaction
        /// </summary>
        [HttpDelete("transactions/{id}")]
        [RequireFinancialManage]
        public async Task<IActionResult> DeleteFinancialTransaction(int id)
        {
            var response = await _financialRepository.DeleteFinancialTransactionAsync(id);
            return NewResult(response);
        }

        #endregion

        #region Treasury Balance and Reports

        /// <summary>
        /// Get treasury balance for specific period
        /// </summary>
        [HttpGet("treasuries/{treasuryId}/balance")]
        [RequireFinancialRead]
        public async Task<IActionResult> GetTreasuryBalance(
            int treasuryId,
            [FromQuery] DateTime fromDate,
            [FromQuery] DateTime toDate)
        {
            var response = await _financialRepository.GetTreasuryBalanceAsync(treasuryId, fromDate, toDate);
            return NewResult(response);
        }

        /// <summary>
        /// Get all treasuries balance for specific period
        /// </summary>
        [HttpGet("treasuries/balance")]
        [RequireFinancialRead]
        public async Task<IActionResult> GetAllTreasuriesBalance(
            [FromQuery] DateTime fromDate,
            [FromQuery] DateTime toDate)
        {
            var response = await _financialRepository.GetAllTreasuriesBalanceAsync(fromDate, toDate);
            return NewResult(response);
        }

        /// <summary>
        /// Get cash flow report for specific period
        /// </summary>
        [HttpGet("reports/cash-flow")]
        [RequireFinancialRead]
        public async Task<IActionResult> GetCashFlowReport(
            [FromQuery] DateTime fromDate,
            [FromQuery] DateTime toDate)
        {
            var response = await _financialRepository.GetCashFlowReportAsync(fromDate, toDate);
            return NewResult(response);
        }

        /// <summary>
        /// Get current balance for specific treasury
        /// </summary>
        [HttpGet("treasuries/{treasuryId}/current-balance")]
        [RequireFinancialRead]
        public async Task<IActionResult> GetTreasuryCurrentBalance(int treasuryId)
        {
            var response = await _financialRepository.GetTreasuryCurrentBalanceAsync(treasuryId);
            return NewResult(response);
        }

        /// <summary>
        /// Get total cash balance across all treasuries
        /// </summary>
        [HttpGet("total-cash-balance")]
        [RequireFinancialRead]
        public async Task<IActionResult> GetTotalCashBalance()
        {
            var response = await _financialRepository.GetTotalCashBalanceAsync();
            return NewResult(response);
        }

        #endregion

        #region Financial Reports (Advanced)

        /// <summary>
        /// Get profit and loss statement
        /// </summary>
        [HttpGet("reports/profit-loss")]
        [RequireFinancialRead]
        public async Task<IActionResult> GetProfitLossStatement(
            [FromQuery] DateTime fromDate,
            [FromQuery] DateTime toDate)
        {
            // This will be implemented in the advanced reporting section
            return Ok(new { message = "Profit & Loss report will be implemented in advanced reporting phase" });
        }

        /// <summary>
        /// Get balance sheet
        /// </summary>
        [HttpGet("reports/balance-sheet")]
        [RequireFinancialRead]
        public async Task<IActionResult> GetBalanceSheet([FromQuery] DateTime asOfDate)
        {
            // This will be implemented in the advanced reporting section
            return Ok(new { message = "Balance sheet will be implemented in advanced reporting phase" });
        }

        #endregion
    }
}
