﻿using GamalCompany.Data.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace GamalCompany.Data.Context
{
    public class ApplicationDbContext : DbContext
    {
        private readonly IHttpContextAccessor? _httpContextAccessor;

        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options, IHttpContextAccessor httpContextAccessor) : base(options)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        //Comapny Main
        public DbSet<Company> Companies { get; set; }
        public DbSet<CompanyDepartment> CompanyDepartments { get; set; }
        public DbSet<Partner> Partners { get; set; }
        public DbSet<PartnerTransation> PartnerTransations { get; set; }

        //Stores
        public DbSet<Unit> Units { get; set; }
        public DbSet<CategoryType> CategoryTypes { get; set; }
        public DbSet<ItemCategory> ItemCategories { get; set; }
        public DbSet<Item> Items { get; set; }
        public DbSet<ItemImage> ItemImages { get; set; }
        public DbSet<InvoiceMaster> InvoiceMasters { get; set; }
        public DbSet<InvoiceDetail> InvoiceDetails { get; set; }
        public DbSet<InventoryTransaction> InventoryTransactions { get; set; }

        //Suppliers Customers
        public DbSet<VanderType> VanderTypes { get; set; }
        public DbSet<SupplierCustomer> SupplierCustomers { get; set; }

        //Suppliers and Customers Transaction
        public DbSet<SupplierCustomerTransaction> SupplierCustomerTransactions { get; set; }


        //Financial Transactions 
        public DbSet<Treasury> Treasuries { get; set; }
        public DbSet<TreasuryTransaction> TreasuryTransactions { get; set; }   // 
        public DbSet<FinancialTransaction> FinancialTransactions { get; set; } //المعاملات المالية 

        //Action Main
        public DbSet<ActionType> ActionTypes { get; set; }
        public DbSet<MainAction> MainActions { get; set; }

        //User and Permissions
        public DbSet<User> Users { get; set; }
        public DbSet<Module> Modules { get; set; }
        public DbSet<UserPermission> UserPermissions { get; set; }
        public DbSet<RefreshToken> RefreshTokens { get; set; }



        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Seed Data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed CategoryType Categories
            modelBuilder.Entity<CategoryType>().HasData(
                new CategoryType { Id = 1, NameEn = "الاصناف", Description = "Component", IsActive = true, SortOrder = 1, Code = "Item", Symbol = "COMP", CreatedBy = 1 , CreatedAt = new DateTime(2025, 1, 1) },
                new CategoryType { Id = 2, NameEn = "منتجات تامة", Description = "FinishedProduct", IsActive = true, SortOrder = 2, Code = "Prod", Symbol = "Prod", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) }
            );

            //Seed VanderType
            modelBuilder.Entity<VanderType>().HasData(
                new VanderType { Id = 1, NameEn = "مورد", Description = "الموردين", IsActive = true, CreatedBy = 1,  CreatedAt = new DateTime(2025, 1, 1) },
                new VanderType { Id = 2, NameEn = "العميل", Description = "العملاء", IsActive = true, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) }
            );

            // Seed Units
            modelBuilder.Entity<Unit>().HasData(
                new Unit { Id = 1,  NameEn= "Piece", NameAr = "قطعة", Symbol = "Pc", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit { Id = 2,  NameEn= "Kilogram", NameAr = "كيلوجرام", Symbol = "Kg", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit { Id = 3,  NameEn= "Gram", NameAr = "جرام", Symbol = "g", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit { Id = 4,  NameEn= "Liter", NameAr = "لتر", Symbol = "L", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit { Id = 5,  NameEn= "Milliliter", NameAr = "مليلتر", Symbol = "ml", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit { Id = 6,  NameEn= "Meter", NameAr = "متر", Symbol = "m", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit { Id = 7,  NameEn= "Centimeter", NameAr = "سنتيمتر", Symbol = "cm", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit { Id = 8,  NameEn= "Box", NameAr = "صندوق", Symbol = "Box", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit { Id = 9,  NameEn= "Carton", NameAr = "كرتونة", Symbol = "Ctn", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit { Id = 10, NameEn = "Pack", NameAr = "عبوة", Symbol = "Pck", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit { Id = 11, NameEn = "Bottle", NameAr = "زجاجة", Symbol = "Btl", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new Unit { Id = 12, NameEn = "Can", NameAr = "علبة", Symbol = "Can", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) }
            );

            //Seed User  //Admin@123
            modelBuilder.Entity<User>().HasData( 
               new User { Id = 1, UserName = "Admin", FullName = "مدير النظام", Password= "eEZPEDAT0eLC131RCecDF38MAyaj3SHPezzQ7iuONHTAAvAHZVnmCR4QfKX3EQIY", Phone = "", ProfileImage = "", IsActive = true, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) }
           );

            //Seed Module Permission
            modelBuilder.Entity<Module>().HasData(
               new Module { Id = 1, NameEn = "Item", Description = "الاصناف", IsActive = true, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
               new Module { Id = 2, NameEn = "Product", Description = "المنتجات", IsActive = true, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
               new Module { Id = 3, NameEn = "Suppliers", Description = "الموردين", IsActive = true, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
               new Module { Id = 4, NameEn = "Customers", Description = "العملاء", IsActive = true, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
               new Module { Id = 5, NameEn = "InvoiceItem", Description = "فواتير الاصناف", IsActive = true, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
               new Module { Id = 6, NameEn = "InvoiceProduct", Description = "فواتير المنتجات", IsActive = true, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
               new Module { Id = 7, NameEn = "System", Description = "الاعدادت", IsActive = true, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
               new Module { Id = 8, NameEn = "User", Description = "المستخدمين", IsActive = true, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
               new Module { Id = 9, NameEn = "Financial", Description = "الحسابات", IsActive = true, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
               new Module { Id = 10, NameEn = "Partner", Description = "الشركاء", IsActive = true, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) }
           );

            //Seed UserPermissions
            modelBuilder.Entity<UserPermission>().HasData(
              new UserPermission { Id = 1,UserId=1, Permission = "Admin", ModuleId = 7, IsActive = true, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
              new UserPermission { Id = 2,UserId=1, Permission = "Manage", ModuleId = 8, IsActive = true, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) }
             );

            //Seed ActionTypes
            modelBuilder.Entity<ActionType>().HasData(
                new ActionType { Id = 1, NameEn = "In", Description = "إضافة", CreatedBy = 1,  CreatedAt = new DateTime(2025, 1, 1) },
                new ActionType { Id = 2, NameEn = "Out", Description = "خصم", CreatedBy = 1,  CreatedAt = new DateTime(2025, 1, 1) },
                new ActionType { Id = 3, NameEn = "NoAction", Description = "تسوية", CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) }
             );

            //Seed Main Actions
            modelBuilder.Entity<MainAction>().HasData(
                new MainAction { Id = 1, NameEn = "مخزن الاصناف", Description = "مخزن الاصناف", ActionTypeId = 3, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 2, NameEn = "الرصيد الافتتاحي", Description = "مخزن الاصناف", ActionTypeId = 1, ParentActionId = 1, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 3, NameEn = "فواتير مخزن الاصناف", Description = "مخزن الاصناف", ActionTypeId = 1, ParentActionId = 1, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 4, NameEn = "مشتريات", Description = "مخزن الاصناف", ActionTypeId = 1, ParentActionId = 3, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 5, NameEn = "مرتجع مشتريات", Description = "مخزن الاصناف", ActionTypeId = 2, ParentActionId = 3, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 6, NameEn = "صرف للاقسام", Description = "مخزن الاصناف", ActionTypeId = 2, ParentActionId = 3, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 7, NameEn = "مرتجع من الاقسام", Description = "مخزن الاصناف", ActionTypeId = 1, ParentActionId = 3, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 8, NameEn = "استلام من الاقسام", Description = "مخزن الاصناف", ActionTypeId = 1, ParentActionId = 3, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 9, NameEn = "تسوية بالاضافة", Description = "مخزن الاصناف", ActionTypeId = 1, ParentActionId = 1, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 10, NameEn = "تسوية بالخصم", Description = "مخزن الاصناف", ActionTypeId = 2, ParentActionId = 1, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                // '--------------------------------------------------------------------------------'
                new MainAction { Id = 11, NameEn = "مخزن المنتج التام", Description = "مخزن التام", ActionTypeId = 3, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 12, NameEn = "الرصيد الافتتاحي", Description = "مخزن التام", ActionTypeId = 1, ParentActionId = 11, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 13, NameEn = "فواتير مخزن المنتج التام", Description = "مخزن التام", ActionTypeId = 3, ParentActionId = 11, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 14, NameEn = "مبيعات", Description = "مخزن التام", ActionTypeId = 2, ParentActionId = 13, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 15, NameEn = "اضافة من الانتاج", Description = "مخزن التام", ActionTypeId = 1, ParentActionId = 13, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 16, NameEn = "مشتريات", Description = "مخزن التام", ActionTypeId = 1, ParentActionId = 13, CreatedBy = 1 , CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 17, NameEn = "مرتجع مشتريات", Description = "مخزن التام", ActionTypeId = 1, ParentActionId = 13, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 18, NameEn = "مرتجع مبيعات", Description = "مخزن التام", ActionTypeId = 1, ParentActionId = 13, CreatedBy = 1 , CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 19, NameEn = "مرتجع للانتاج", Description = "مخزن التام", ActionTypeId = 2, ParentActionId = 13, CreatedBy = 1 , CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 20, NameEn = "ضبط المخزون بالاضافة", Description = "مخزن التام", ActionTypeId = 1, ParentActionId = 11, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 21, NameEn = "ضبط المخزون بالخصم", Description = "مخزن التام", ActionTypeId = 2, ParentActionId = 11, CreatedBy = 1 , CreatedAt = new DateTime(2025, 1, 1) },
                // '-----------------------------------------------------------------------------------'
                new MainAction { Id = 22, NameEn = "حركات الموردين والعملاء", Description = "مورد وعميل", ActionTypeId = 3, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 23, NameEn = "رصيد سابق مدين", Description = "مورد وعميل", ActionTypeId = 1, ParentActionId = 22, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 24, NameEn = "رصيد سابق دائن", Description = "مورد وعميل", ActionTypeId = 2, ParentActionId = 22, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 25, NameEn = "سداد دفعة نقدية", Description = "مورد وعميل", ActionTypeId = 1, ParentActionId = 22, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 26, NameEn = "تحصيل دفعة نقدية", Description = "مورد وعميل", ActionTypeId = 2, ParentActionId = 22, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 27, NameEn = "مشتريات", Description = "مورد وعميل", ActionTypeId = 1, ParentActionId = 22, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 28, NameEn = "مبيعات", Description = "مورد وعميل", ActionTypeId = 2, ParentActionId = 22, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 29, NameEn = "مرتجع مشتريات", Description = "مورد وعميل", ActionTypeId = 2, ParentActionId = 22, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) },
                new MainAction { Id = 30, NameEn = "مرتجع مبيعات", Description = "مورد وعميل", ActionTypeId = 1, ParentActionId = 22, CreatedBy = 1, CreatedAt = new DateTime(2025, 1, 1) }
             // '-----------------------------------------------------------------------------------------'


             );


        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            ApplyAuditTrail();
            return await base.SaveChangesAsync(cancellationToken);
        }

        public override int SaveChanges()
        {
            ApplyAuditTrail();
            return base.SaveChanges();
        }

        private void ApplyAuditTrail()
        {
            var currentUserId = GetCurrentUserId();
            var currentTime = DateTime.Now;

            foreach (var entry in ChangeTracker.Entries<BaseEntity>())
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        entry.Entity.CreatedAt = currentTime;
                        if (currentUserId.HasValue)
                            entry.Entity.CreatedBy = currentUserId.Value;
                        break;

                    case EntityState.Modified:
                        entry.Entity.UpdatedAt = currentTime;
                        if (currentUserId.HasValue)
                            entry.Entity.UpdatedBy = currentUserId.Value;
                        // Prevent modification of creation audit fields
                        entry.Property(e => e.CreatedAt).IsModified = false;
                        entry.Property(e => e.CreatedBy).IsModified = false;
                        break;
                }
            }
        }

        private int? GetCurrentUserId()
        {
            try
            {
                var userIdClaim = _httpContextAccessor?.HttpContext?.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (int.TryParse(userIdClaim, out int userId))
                {
                    return userId;
                }
            }
            catch
            {
                // If we can't get the user ID, return null (system operation)
            }
            return null;
        }
    }
}
