using System.ComponentModel.DataAnnotations;

namespace GamalComapany.Service.Dtos.InventoryDto
{
    public class CreateInventoryTransactionDto
    {
        public int? InvoiceId { get; set; }

        [Required(ErrorMessage = "نوع العملية مطلوب")]
        public int ActionTypeId { get; set; }

        [Required(ErrorMessage = "تاريخ المعاملة مطلوب")]
        public DateTime DateTransaction { get; set; }

        [Required(ErrorMessage = "الصنف مطلوب")]
        public int ItemId { get; set; }

        [Required(ErrorMessage = "الوحدة مطلوبة")]
        public int UnitId { get; set; }

        [Required(ErrorMessage = "الكمية مطلوبة")]
        [Range(1, int.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من صفر")]
        public int Quantity { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "سعر الوحدة يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? UnitPrice { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "المبلغ الإجمالي يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? TotalAmount { get; set; }
    }

    public class InventoryTransactionResponseDto
    {
        public int Id { get; set; }
        public int? InvoiceId { get; set; }
        public string? InvoiceNumber { get; set; }
        public int? ActionTypeId { get; set; }
        public string ActionTypeName { get; set; } = string.Empty;
        public DateTime DateTransaction { get; set; }
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string ItemCode { get; set; } = string.Empty;
        public int UnitId { get; set; }
        public string UnitName { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public decimal? UnitPrice { get; set; }
        public decimal? TotalAmount { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class ItemStockDto
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string ItemCode { get; set; } = string.Empty;
        public string CategoryName { get; set; } = string.Empty;
        public string UnitName { get; set; } = string.Empty;
        public decimal CurrentStock { get; set; }
        public decimal? MinimumStock { get; set; }
        public decimal? MaximumStock { get; set; }
        public decimal? ReorderLevel { get; set; }
        public decimal StandardCost { get; set; }
        public decimal TotalValue { get; set; }
        public bool IsLowStock { get; set; }
        public DateTime LastTransactionDate { get; set; }
    }

    public class InventoryMovementDto
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public decimal OpeningBalance { get; set; }
        public decimal TotalIn { get; set; }
        public decimal TotalOut { get; set; }
        public decimal ClosingBalance { get; set; }
        public List<InventoryTransactionResponseDto> Transactions { get; set; } = new();
    }

    public class CreateItemImageDto
    {
        [Required(ErrorMessage = "معرف الصنف مطلوب")]
        public int ItemId { get; set; }

        [Required(ErrorMessage = "مسار الصورة مطلوب")]
        [StringLength(500, ErrorMessage = "مسار الصورة يجب أن يكون أقل من 500 حرف")]
        public string ImageUrl { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "عنوان الصورة يجب أن يكون أقل من 100 حرف")]
        public string? ImageTitle { get; set; }

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? ImageDescription { get; set; }

        public bool IsPrimary { get; set; } = false;
        public int SortOrder { get; set; } = 1;
    }

    public class ItemImageResponseDto
    {
        public int Id { get; set; }
        public int ItemId { get; set; }
        public string ImageUrl { get; set; } = string.Empty;
        public string? ImageTitle { get; set; }
        public string? ImageDescription { get; set; }
        public bool IsPrimary { get; set; }
        public int SortOrder { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class UpdateItemDto
    {
        [Required(ErrorMessage = "معرف الصنف مطلوب")]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم الصنف مطلوب")]
        [StringLength(100, ErrorMessage = "اسم الصنف يجب أن يكون أقل من 100 حرف")]
        public string NameEn { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "الاسم العربي يجب أن يكون أقل من 100 حرف")]
        public string? NameAr { get; set; }

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string Description { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "كود الصنف يجب أن يكون أقل من 50 حرف")]
        public string? ItemCode { get; set; }

        [Required(ErrorMessage = "التصنيف مطلوب")]
        public int CategoryId { get; set; }

        [StringLength(50, ErrorMessage = "الباركود يجب أن يكون أقل من 50 حرف")]
        public string? Barcode { get; set; }

        [Required(ErrorMessage = "الوحدة مطلوبة")]
        public int UnitId { get; set; }

        [Required(ErrorMessage = "التكلفة القياسية مطلوبة")]
        [Range(0, double.MaxValue, ErrorMessage = "التكلفة القياسية يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal StandardCost { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "الحد الأدنى للمخزون يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? MinimumStock { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "الحد الأقصى للمخزون يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? MaximumStock { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "مستوى إعادة الطلب يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? ReorderLevel { get; set; }

        public int? SortOrder { get; set; } = 1;

        [Range(1, 2, ErrorMessage = "نوع الصنف يجب أن يكون 1 (صنف) أو 2 (منتج)")]
        public int? ItemType { get; set; } = 1;

        public DateTime? UpdatedAt { get; set; }
        public int? UpdatedBy { get; set; }
    }

    public class ItemResponseDto
    {
        public int Id { get; set; }
        public string NameEn { get; set; } = string.Empty;
        public string? NameAr { get; set; }
        public string Description { get; set; } = string.Empty;
        public string? ItemCode { get; set; }
        public int CategoryId { get; set; }
        public string CategoryName { get; set; } = string.Empty;
        public string? Barcode { get; set; }
        public int UnitId { get; set; }
        public string UnitName { get; set; } = string.Empty;
        public decimal StandardCost { get; set; }
        public decimal? MinimumStock { get; set; }
        public decimal? MaximumStock { get; set; }
        public decimal? ReorderLevel { get; set; }
        public int? SortOrder { get; set; }
        public int? ItemType { get; set; }
        public string ItemTypeName { get; set; } = string.Empty;
        public decimal CurrentStock { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public List<ItemImageResponseDto> Images { get; set; } = new();
    }
}
