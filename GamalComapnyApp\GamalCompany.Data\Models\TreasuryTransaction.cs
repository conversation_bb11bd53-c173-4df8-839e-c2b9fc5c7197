﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalCompany.Data.Models
{
    public class TreasuryTransaction : BaseEntity
    {
        public int TreasuryId { get; set; }
        public int ActionTypeId { get; set; } // Add  // Min
        public int FinancialTransactionId { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal Amount { get; set; }
        public DateTime TransactionDate { get; set; }
        public string? ReferenceNumber { get; set; }
        public string? Description { get; set; }
        public string? ReferenceType { get; set; } // "Customer", "Supplier", "Partner", "Expense"
        public int ReferenceId { get; set; }
        public string? Notes { get; set; }
        public string? ImagePath { get; set; }

        [ForeignKey(nameof(ActionTypeId))]
        public virtual MainAction MainAction { get; set; } = null!;

        [ForeignKey(nameof(TreasuryId))]
        public virtual Treasury Treasury { get; set; } = null!;
      
        [ForeignKey(nameof(FinancialTransactionId))]
        public FinancialTransaction FinancialTransaction { get; set; } = null!;

    }
}
