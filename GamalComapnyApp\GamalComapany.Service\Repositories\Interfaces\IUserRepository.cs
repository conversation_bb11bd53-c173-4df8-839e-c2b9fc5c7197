using GamalComapany.Service.Dtos;
using GamalComapany.Service.Dtos.UserDto;

namespace GamalComapany.Service.Repositories.Interfaces
{
    public interface IUserRepository
    {
        // User Management
        Task<ApiResponse<List<UserResponseDto>>> GetAllUsersAsync();
        Task<ApiResponse<UserResponseDto>> GetUserByIdAsync(int id);
        Task<ApiResponse<UserResponseDto>> GetUserByUsernameAsync(string username);
        Task<ApiResponse<UserResponseDto>> CreateUserAsync(CreateUserDto userDto);
        Task<ApiResponse<UserResponseDto>> UpdateUserAsync(UpdateUserDto userDto);
        Task<ApiResponse<UserResponseDto>> UpdateUserProfileAsync(UpdateUserProfileDto userDto);
        Task<ApiResponse<ProfileImageResponseDto>> UpdateProfileImageAsync(UpdateProfileImageDto updateDto);
        Task<ApiResponse<bool>> DeleteProfileImageAsync(int userId);
        Task<ApiResponse<bool>> DeleteUserAsync(int id);
        Task<ApiResponse<bool>> ChangePasswordAsync(ChangePasswordDto changePasswordDto);
        Task<ApiResponse<bool>> ResetPasswordAsync(int userId, string newPassword);
        
        // Authentication
        Task<ApiResponse<LoginResponseDto>> LoginAsync(LoginDto loginDto);
        Task<ApiResponse<LoginResponseDto>> RefreshTokenAsync(RefreshTokenDto refreshTokenDto);
        Task<ApiResponse<bool>> LogoutAsync(int userId);
        Task<ApiResponse<bool>> ValidateTokenAsync(string token);
        
        // User Permissions
        Task<ApiResponse<List<UserPermissionResponseDto>>> GetUserPermissionsAsync(int userId);
        Task<ApiResponse<UserPermissionResponseDto>> CreateUserPermissionAsync(CreateUserPermissionDto permissionDto);
        Task<ApiResponse<bool>> DeleteUserPermissionAsync(int permissionId);
        Task<ApiResponse<UserPermissionSummaryDto>> GetUserPermissionSummaryAsync(int userId);
        Task<ApiResponse<bool>> HasPermissionAsync(int userId, string moduleName, string permission);
        
        // Module Management
        Task<ApiResponse<List<ModuleResponseDto>>> GetAllModulesAsync();
        Task<ApiResponse<ModuleResponseDto>> GetModuleByIdAsync(int id);
        Task<ApiResponse<ModuleResponseDto>> CreateModuleAsync(CreateModuleDto moduleDto);
        Task<ApiResponse<ModuleResponseDto>> UpdateModuleAsync(UpdateModuleDto moduleDto);
        Task<ApiResponse<bool>> DeleteModuleAsync(int id);
    }
}
