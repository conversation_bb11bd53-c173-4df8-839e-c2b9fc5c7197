﻿using GamalCompany.Data.Enum;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalCompany.Data.Models
{
    public class FinancialTransaction : BaseEntity
    {
        public int TransactionTypeId { get; set; }
        public bool IsInflow { get; set; } // true = وارد, false = منصرف
        public DateTime TransactionDate { get; set; }
       
        [Column(TypeName = "decimal(18,4)")]
        public decimal? Amount { get; set; }  
        public RefranseFinancicalEnum ReferenceType { get; set; } 
        public int ReferenceId { get; set; }
        public string? Description { get; set; }

        [ForeignKey(nameof(TransactionTypeId))]
        public MainAction TransactionType { get; set; } = null!;
    
    }
}
