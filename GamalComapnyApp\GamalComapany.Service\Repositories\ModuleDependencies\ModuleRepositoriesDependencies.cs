﻿using GamalComapany.Service.Repositories.Implementations;
using GamalComapany.Service.Repositories.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalComapany.Service.Repositories.ModuleDependencies
{
    public static class ModuleRepositoriesDependencies
    {
        public static IServiceCollection AddRepositoriesDependencies(this IServiceCollection services)
        {
            // Generic Repository
            services.AddTransient(typeof(IGenericRepository<>), typeof(GenericRepository<>));

            // Unit of Work
            services.AddScoped<IUnitOfWorkOfService, UnitOfWorkOfService>();
            services.AddScoped<IUnitOfWork, UnitOfWork>();

            // Individual Repository Services
            services.AddScoped<IItemRepository, ItemService>();
            services.AddScoped<IPartnerRepository, PartnerService>();
            services.AddScoped<IUserRepository, UserService>();
            services.AddScoped<IInventoryRepository, InventoryService>();
            services.AddScoped<IFinancialRepository, FinancialService>();
            // TODO: Add other repository services when implemented
            // services.AddScoped<ISupplierCustomerRepository, SupplierCustomerService>();

            return services;
        }
    }
}
