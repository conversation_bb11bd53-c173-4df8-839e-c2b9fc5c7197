2025-06-24 11:29:23.825 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-24 11:29:24.195 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-24 11:29:24.301 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 11:29:24.303 +03:00 [INF] Hosting environment: Development
2025-06-24 11:29:24.304 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-24 11:29:48.304 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 11:29:48.615 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 317.9145ms
2025-06-24 11:29:48.694 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/swagger-ui.css - null null
2025-06-24 11:29:48.696 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/swagger-ui-bundle.js - null null
2025-06-24 11:29:48.703 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.js - null null
2025-06-24 11:29:48.694 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.css - null null
2025-06-24 11:29:48.703 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/swagger-ui-standalone-preset.js - null null
2025-06-24 11:29:48.703 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - null null
2025-06-24 11:29:48.726 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.js - 200 null application/javascript;charset=utf-8 27.2036ms
2025-06-24 11:29:48.753 +03:00 [INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
2025-06-24 11:29:48.757 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - 200 16523 application/javascript; charset=utf-8 53.7301ms
2025-06-24 11:29:48.762 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_vs/browserLink - null null
2025-06-24 11:29:48.764 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.css - 200 202 text/css 69.7286ms
2025-06-24 11:29:48.770 +03:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-06-24 11:29:48.766 +03:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-06-24 11:29:48.828 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/swagger-ui-standalone-preset.js - 200 229223 text/javascript 128.9287ms
2025-06-24 11:29:48.828 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/swagger-ui.css - 200 154949 text/css 136.4819ms
2025-06-24 11:29:48.830 +03:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-06-24 11:29:48.857 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/swagger-ui-bundle.js - 200 1484234 text/javascript 162.7342ms
2025-06-24 11:29:48.880 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_vs/browserLink - 200 null text/javascript; charset=UTF-8 118.3759ms
2025-06-24 11:29:49.137 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 11:29:49.169 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/favicon-32x32.png - null null
2025-06-24 11:29:49.174 +03:00 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-06-24 11:29:49.176 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/favicon-32x32.png - 200 628 image/png 6.7264ms
2025-06-24 11:29:49.323 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 186.3708ms
2025-06-24 11:30:42.878 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 11:30:42.954 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 11:30:42.956 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:30:42.962 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 11:30:42.965 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 401 0 null 118.7828ms
2025-06-24 11:30:44.087 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 11:30:44.094 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 11:30:44.096 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:30:44.097 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 11:30:44.101 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 401 0 null 14.015ms
2025-06-24 11:35:04.111 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-24 11:35:04.172 +03:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-06-24 11:35:04.216 +03:00 [INF] Executed DbCommand (41ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_getapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session', @LockMode = 'Exclusive';
SELECT @result
2025-06-24 11:35:04.301 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
2025-06-24 11:35:04.309 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-24 11:35:04.310 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-06-24 11:35:04.325 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-06-24 11:35:04.335 +03:00 [INF] Applying migration '20250624083344_NewTables'.
2025-06-24 11:35:04.562 +03:00 [INF] Executed DbCommand (109ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @var sysname;
SELECT @var = [d].[name]
FROM [sys].[default_constraints] [d]
INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
WHERE ([d].[parent_object_id] = OBJECT_ID(N'[ItemImages]') AND [c].[name] = N'IsPrimary');
IF @var IS NOT NULL EXEC(N'ALTER TABLE [ItemImages] DROP CONSTRAINT [' + @var + '];');
ALTER TABLE [ItemImages] ALTER COLUMN [IsPrimary] bit NOT NULL;
2025-06-24 11:35:04.582 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [RefreshTokens] (
    [Id] int NOT NULL IDENTITY,
    [Token] nvarchar(max) NOT NULL,
    [UserId] int NOT NULL,
    [ExpiryDate] datetime2 NOT NULL,
    [IsRevoked] bit NOT NULL,
    [RevokedReason] nvarchar(max) NULL,
    [RevokedAt] datetime2 NULL,
    [IpAddress] nvarchar(max) NULL,
    [UserAgent] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_RefreshTokens] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_RefreshTokens_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);
2025-06-24 11:35:04.596 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE [Users] SET [Password] = N'Admin@123'
WHERE [Id] = 1;
SELECT @@ROWCOUNT;
2025-06-24 11:35:04.603 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_RefreshTokens_UserId] ON [RefreshTokens] ([UserId]);
2025-06-24 11:35:04.610 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250624083344_NewTables', N'9.0.5');
2025-06-24 11:35:04.634 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_releaseapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session';
SELECT @result
2025-06-24 11:37:35.559 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-24 11:37:35.829 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-24 11:37:35.898 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 11:37:35.900 +03:00 [INF] Hosting environment: Development
2025-06-24 11:37:35.901 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-24 11:37:40.760 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 11:37:40.993 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 238.1812ms
2025-06-24 11:37:41.032 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - null null
2025-06-24 11:37:41.041 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_vs/browserLink - null null
2025-06-24 11:37:41.045 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - 200 16523 application/javascript; charset=utf-8 13.3981ms
2025-06-24 11:37:41.083 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_vs/browserLink - 200 null text/javascript; charset=UTF-8 42.1737ms
2025-06-24 11:37:41.379 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 11:37:41.564 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 184.8775ms
2025-06-24 11:37:55.181 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 11:37:55.275 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 11:37:55.277 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:37:55.281 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 11:37:55.284 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 401 0 null 103.6515ms
2025-06-24 11:37:56.416 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 11:37:56.426 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 11:37:56.427 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:37:56.429 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 11:37:56.430 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 401 0 null 13.7564ms
2025-06-24 11:38:17.607 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/login - application/json 52
2025-06-24 11:38:17.614 +03:00 [INF] CORS policy execution successful.
2025-06-24 11:38:17.631 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 11:38:17.671 +03:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(GamalComapany.Service.Dtos.UserDto.LoginDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 11:38:19.944 +03:00 [INF] Executed DbCommand (66ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 11:38:20.088 +03:00 [ERR] Error verifying password
System.FormatException: The input is not a valid Base-64 string as it contains a non-base 64 character, more than two padding characters, or an illegal character among the padding characters.
   at System.Convert.FromBase64CharPtr(Char* inputPtr, Int32 inputLength)
   at System.Convert.FromBase64String(String s)
   at GamalComapany.Service.Authentication.PasswordHashingService.VerifyPassword(String password, String hashedPassword) in D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapany.Service\Authentication\PasswordHashingService.cs:line 61
2025-06-24 11:38:20.104 +03:00 [WRN] Login attempt with invalid password for user: Admin
2025-06-24 11:38:20.116 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.LoginResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:38:20.144 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API) in 2465.6519ms
2025-06-24 11:38:20.147 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 11:38:20.159 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/login - 401 null application/json; charset=utf-8 2551.2116ms
2025-06-24 11:39:23.938 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User - application/json 123
2025-06-24 11:39:23.943 +03:00 [INF] CORS policy execution successful.
2025-06-24 11:39:23.947 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 11:39:23.948 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:39:23.949 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 11:39:23.950 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User - 401 0 null 44.6553ms
2025-06-24 11:39:29.957 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User - application/json 123
2025-06-24 11:39:29.960 +03:00 [INF] CORS policy execution successful.
2025-06-24 11:39:29.961 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 11:39:29.963 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:39:29.964 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 11:39:29.966 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User - 401 0 null 8.9191ms
2025-06-24 11:41:14.653 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-24 11:41:14.965 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-24 11:41:15.037 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 11:41:15.038 +03:00 [INF] Hosting environment: Development
2025-06-24 11:41:15.039 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-24 11:41:32.791 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 11:41:33.026 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 240.6584ms
2025-06-24 11:41:33.045 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - null null
2025-06-24 11:41:33.045 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_vs/browserLink - null null
2025-06-24 11:41:33.061 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - 200 16523 application/javascript; charset=utf-8 16.0382ms
2025-06-24 11:41:33.092 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_vs/browserLink - 200 null text/javascript; charset=UTF-8 47.1984ms
2025-06-24 11:41:33.232 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 11:41:33.403 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 170.7773ms
2025-06-24 11:42:15.348 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User - application/json 120
2025-06-24 11:42:15.357 +03:00 [INF] CORS policy execution successful.
2025-06-24 11:42:15.444 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.CreateUser (GamalComapnyApp.API)'
2025-06-24 11:42:15.469 +03:00 [INF] Route matched with {action = "CreateUser", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateUser(GamalComapany.Service.Dtos.UserDto.CreateUserDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 11:42:17.498 +03:00 [INF] Executed DbCommand (51ms) [Parameters=[@__userDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[UserName] = @__userDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
2025-06-24 11:42:17.542 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.UserResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:42:17.576 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.CreateUser (GamalComapnyApp.API) in 2101.5823ms
2025-06-24 11:42:17.579 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.CreateUser (GamalComapnyApp.API)'
2025-06-24 11:42:17.588 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User - 400 null application/json; charset=utf-8 2240.1249ms
2025-06-24 11:42:54.673 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User - application/json 120
2025-06-24 11:42:54.694 +03:00 [INF] CORS policy execution successful.
2025-06-24 11:42:54.735 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.CreateUser (GamalComapnyApp.API)'
2025-06-24 11:42:54.737 +03:00 [INF] Route matched with {action = "CreateUser", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateUser(GamalComapany.Service.Dtos.UserDto.CreateUserDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 11:42:54.805 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[UserName] = @__userDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
2025-06-24 11:42:55.152 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (Size = 4000), @p6='?' (Size = 4000), @p7='?' (Size = 4000), @p8='?' (DbType = DateTime2), @p9='?' (DbType = Int32), @p10='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Users] ([CreatedAt], [CreatedBy], [FullName], [IsActive], [IsDeleted], [Password], [Phone], [ProfileImage], [UpdatedAt], [UpdatedBy], [UserName])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10);
2025-06-24 11:42:55.184 +03:00 [INF] User created successfully: Admin
2025-06-24 11:42:55.310 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 11:42:55.330 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.UserResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:42:55.338 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.CreateUser (GamalComapnyApp.API) in 598.1489ms
2025-06-24 11:42:55.340 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.CreateUser (GamalComapnyApp.API)'
2025-06-24 11:42:55.341 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User - 200 null application/json; charset=utf-8 673.5844ms
2025-06-24 11:43:58.496 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/login - application/json 52
2025-06-24 11:43:58.505 +03:00 [INF] CORS policy execution successful.
2025-06-24 11:43:58.508 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 11:43:58.512 +03:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(GamalComapany.Service.Dtos.UserDto.LoginDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 11:43:58.570 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 11:43:58.713 +03:00 [INF] Access token generated for user 2
2025-06-24 11:43:58.761 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-06-24 11:43:58.770 +03:00 [INF] User logged in successfully: Admin
2025-06-24 11:43:58.772 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.LoginResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:43:58.783 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API) in 266.5947ms
2025-06-24 11:43:58.785 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 11:43:58.787 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/login - 200 null application/json; charset=utf-8 295.4586ms
2025-06-24 11:44:18.768 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 11:44:18.794 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 11:44:18.797 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:44:18.803 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 11:44:18.805 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 401 0 null 36.7278ms
2025-06-24 11:44:20.139 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 11:44:20.143 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 11:44:20.145 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:44:20.146 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 11:44:20.162 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 401 0 null 22.8888ms
2025-06-24 11:44:50.353 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 11:44:50.359 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 11:44:50.360 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:44:50.362 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 11:44:50.363 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 401 0 null 15.5271ms
2025-06-24 11:45:18.321 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 11:45:18.524 +03:00 [WRN] Authorization failed for user 2: Missing permission User:Manage
2025-06-24 11:45:18.526 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:45:18.529 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-06-24 11:45:18.531 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 403 0 null 212.1536ms
2025-06-24 11:46:11.558 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 11:46:11.562 +03:00 [WRN] Authorization failed for user 2: Missing permission User:Manage
2025-06-24 11:46:11.564 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:46:11.565 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-06-24 11:46:11.567 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 403 0 null 59.3793ms
2025-06-24 11:46:42.499 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 11:46:42.502 +03:00 [WRN] Authorization failed for user 2: Missing permission User:Manage
2025-06-24 11:46:42.503 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:46:42.504 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-06-24 11:46:42.519 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 403 0 null 20.0392ms
2025-06-24 11:57:05.128 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-24 11:57:05.504 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-24 11:57:05.621 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 11:57:05.664 +03:00 [INF] Hosting environment: Development
2025-06-24 11:57:05.665 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-24 11:58:01.101 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 11:58:01.369 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 272.9856ms
2025-06-24 11:58:01.393 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_vs/browserLink - null null
2025-06-24 11:58:01.393 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - null null
2025-06-24 11:58:01.400 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - 200 16523 application/javascript; charset=utf-8 7.5158ms
2025-06-24 11:58:01.444 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_vs/browserLink - 200 null text/javascript; charset=UTF-8 50.6036ms
2025-06-24 11:58:01.709 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 11:58:01.867 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 158.3282ms
2025-06-24 11:58:19.021 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/modules - null null
2025-06-24 11:58:19.170 +03:00 [WRN] Authorization failed for user 2: Missing permission User:Manage
2025-06-24 11:58:19.172 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:58:19.175 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-06-24 11:58:19.177 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/modules - 403 0 null 156.5093ms
2025-06-24 11:58:20.609 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/modules - null null
2025-06-24 11:58:20.620 +03:00 [WRN] Authorization failed for user 2: Missing permission User:Manage
2025-06-24 11:58:20.622 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:58:20.623 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-06-24 11:58:20.625 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/modules - 403 0 null 16.1287ms
2025-06-24 12:50:09.006 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/logout - null 0
2025-06-24 12:50:09.014 +03:00 [INF] CORS policy execution successful.
2025-06-24 12:50:09.020 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 09:50:09 ص'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-24 12:50:09.026 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 09:50:09 ص'.
2025-06-24 12:50:09.031 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 09:50:09 ص'.
2025-06-24 12:50:09.035 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-24 12:50:09.041 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 12:50:09.044 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/logout - 401 0 null 38.033ms
2025-06-24 12:50:12.697 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/logout - null 0
2025-06-24 12:50:12.701 +03:00 [INF] CORS policy execution successful.
2025-06-24 12:50:12.703 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 09:50:12 ص'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-24 12:50:12.706 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 09:50:12 ص'.
2025-06-24 12:50:12.723 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 09:50:12 ص'.
2025-06-24 12:50:12.726 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-24 12:50:12.727 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 12:50:12.728 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/logout - 401 0 null 31.4178ms
2025-06-24 12:50:56.963 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/login - application/json 52
2025-06-24 12:50:56.967 +03:00 [INF] CORS policy execution successful.
2025-06-24 12:50:56.971 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 12:50:56.997 +03:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(GamalComapany.Service.Dtos.UserDto.LoginDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 12:51:00.170 +03:00 [INF] Executed DbCommand (101ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 12:51:00.355 +03:00 [INF] Access token generated for user 2
2025-06-24 12:51:00.581 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-06-24 12:51:00.611 +03:00 [INF] User logged in successfully: Admin
2025-06-24 12:51:00.620 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.LoginResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 12:51:00.653 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API) in 3649.5709ms
2025-06-24 12:51:00.656 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 12:51:00.665 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/login - 200 null application/json; charset=utf-8 3704.9677ms
2025-06-24 12:51:43.494 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 12:51:43.524 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.GetAllUsers (GamalComapnyApp.API)'
2025-06-24 12:51:43.529 +03:00 [INF] Route matched with {action = "GetAllUsers", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers() on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 12:51:43.620 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
WHERE [u].[IsDeleted] = CAST(0 AS bit)
ORDER BY [u].[Id], [s].[Id]
2025-06-24 12:51:43.641 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.UserDto.UserResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 12:51:43.655 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.GetAllUsers (GamalComapnyApp.API) in 121.4794ms
2025-06-24 12:51:43.658 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.GetAllUsers (GamalComapnyApp.API)'
2025-06-24 12:51:43.660 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 200 null application/json; charset=utf-8 170.0011ms
2025-06-24 12:52:01.345 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/modules - null null
2025-06-24 12:52:01.349 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.GetAllModules (GamalComapnyApp.API)'
2025-06-24 12:52:01.356 +03:00 [INF] Route matched with {action = "GetAllModules", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllModules() on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 12:52:01.424 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[Id], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[IsActive], [m].[IsDeleted], [m].[NameEn], [m].[UpdatedAt], [m].[UpdatedBy], [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[IsActive], [u].[IsDeleted], [u].[ModuleId], [u].[Permission], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserId]
FROM [Modules] AS [m]
LEFT JOIN [UserPermissions] AS [u] ON [m].[Id] = [u].[ModuleId]
WHERE [m].[IsDeleted] = CAST(0 AS bit)
ORDER BY [m].[Id]
2025-06-24 12:52:01.435 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.UserDto.ModuleResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 12:52:01.440 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.GetAllModules (GamalComapnyApp.API) in 69.8558ms
2025-06-24 12:52:01.443 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.GetAllModules (GamalComapnyApp.API)'
2025-06-24 12:52:01.445 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/modules - 200 null application/json; charset=utf-8 99.8282ms
2025-06-24 12:52:24.779 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Categroy - null null
2025-06-24 12:52:24.803 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.CategroyController.GetCategory (GamalComapnyApp.API)'
2025-06-24 12:52:24.810 +03:00 [INF] Route matched with {action = "GetCategory", controller = "Categroy"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCategory() on controller GamalComapnyApp.API.Controllers.CategroyController (GamalComapnyApp.API).
2025-06-24 12:52:24.831 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [i].[Id], [i].[CategoryTypeId], [i].[Code], [i].[CreatedAt], [i].[CreatedBy], [i].[Description], [i].[ImageUrl], [i].[IsActive], [i].[IsDeleted], [i].[NameEn], [i].[ParentCategoryId], [i].[SortOrder], [i].[Symbol], [i].[UpdatedAt], [i].[UpdatedBy]
FROM [ItemCategories] AS [i]
2025-06-24 12:52:24.834 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalCompany.Data.Models.ItemCategory, GamalCompany.Data, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 12:52:24.842 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.CategroyController.GetCategory (GamalComapnyApp.API) in 28.7744ms
2025-06-24 12:52:24.844 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.CategroyController.GetCategory (GamalComapnyApp.API)'
2025-06-24 12:52:24.848 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Categroy - 404 null application/json; charset=utf-8 73.2456ms
2025-06-24 12:54:29.235 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/Image/profile - multipart/form-data; boundary=----WebKitFormBoundary1jPpY7ToeMLjEJZX 338675
2025-06-24 12:54:29.270 +03:00 [INF] CORS policy execution successful.
2025-06-24 12:54:29.275 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.ImageController.UploadProfileImage (GamalComapnyApp.API)'
2025-06-24 12:54:29.287 +03:00 [INF] Route matched with {action = "UploadProfileImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] UploadProfileImage(Microsoft.AspNetCore.Http.IFormFile) on controller GamalComapnyApp.API.Controllers.ImageController (GamalComapnyApp.API).
2025-06-24 12:54:29.514 +03:00 [INF] User profile images deleted for user: 2
2025-06-24 12:54:29.588 +03:00 [WRN] Could not optimize image: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp\wwwroot\uploads\users\user_2_20250624095429.png
System.IO.IOException: The process cannot access the file 'D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp\wwwroot\uploads\users\user_2_20250624095429.png' because it is being used by another process.
   at Microsoft.Win32.SafeHandles.SafeFileHandle.CreateFile(String fullPath, FileMode mode, FileAccess access, FileShare share, FileOptions options)
   at Microsoft.Win32.SafeHandles.SafeFileHandle.Open(String fullPath, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Nullable`1 unixCreateMode)
   at System.IO.Strategies.OSFileStreamStrategy..ctor(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Nullable`1 unixCreateMode)
   at System.IO.Strategies.FileStreamHelpers.ChooseStrategyCore(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Nullable`1 unixCreateMode)
   at System.IO.FileStream..ctor(String path, FileStreamOptions options)
   at System.IO.File.Open(String path, FileStreamOptions options)
   at SixLabors.ImageSharp.IO.LocalFileSystem.OpenReadAsynchronous(String path)
   at SixLabors.ImageSharp.Image.LoadAsync(DecoderOptions options, String path, CancellationToken cancellationToken)
   at GamalComapany.Service.Services.ImageService.OptimizeImageAsync(String filePath) in D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapany.Service\Services\ImageService.cs:line 332
2025-06-24 12:54:29.608 +03:00 [INF] Image uploaded successfully: uploads/users/user_2_20250624095429.png
2025-06-24 12:54:29.610 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType0`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 12:54:29.615 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.ImageController.UploadProfileImage (GamalComapnyApp.API) in 325.6241ms
2025-06-24 12:54:29.618 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.ImageController.UploadProfileImage (GamalComapnyApp.API)'
2025-06-24 12:54:29.621 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/Image/profile - 200 null application/json; charset=utf-8 385.7389ms
2025-06-24 12:57:26.317 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 12:57:26.322 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.GetAllUsers (GamalComapnyApp.API)'
2025-06-24 12:57:26.323 +03:00 [INF] Route matched with {action = "GetAllUsers", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers() on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 12:57:26.381 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
WHERE [u].[IsDeleted] = CAST(0 AS bit)
ORDER BY [u].[Id], [s].[Id]
2025-06-24 12:57:26.387 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.UserDto.UserResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 12:57:26.389 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.GetAllUsers (GamalComapnyApp.API) in 64.2847ms
2025-06-24 12:57:26.390 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.GetAllUsers (GamalComapnyApp.API)'
2025-06-24 12:57:26.396 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 200 null application/json; charset=utf-8 78.8411ms
2025-06-24 12:58:40.497 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/profile - null null
2025-06-24 12:58:40.529 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.GetProfile (GamalComapnyApp.API)'
2025-06-24 12:58:40.539 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 12:58:40.583 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 12:58:40.593 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.UserResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 12:58:40.600 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.GetProfile (GamalComapnyApp.API) in 58.2852ms
2025-06-24 12:58:40.601 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.GetProfile (GamalComapnyApp.API)'
2025-06-24 12:58:40.603 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/profile - 200 null application/json; charset=utf-8 143.6198ms
2025-06-24 13:01:47.988 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/uploads/users/user_2_20250624095429 - null null
2025-06-24 13:01:48.013 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/uploads/users/user_2_20250624095429 - 404 0 null 28.1538ms
2025-06-24 13:01:48.018 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5250/uploads/users/user_2_20250624095429, Response status code: 404
2025-06-24 13:02:24.868 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/uploads/users/user_2_20250624095429.png - null null
2025-06-24 13:02:25.192 +03:00 [INF] Sending file. Request path: '/uploads/users/user_2_20250624095429.png'. Physical path: 'D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp\wwwroot\uploads\users\user_2_20250624095429.png'
2025-06-24 13:02:25.194 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/uploads/users/user_2_20250624095429.png - ********** image/png 326.1658ms
2025-06-24 13:02:25.296 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/favicon.ico - null null
2025-06-24 13:02:25.299 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/favicon.ico - 404 0 null 3.5629ms
2025-06-24 13:02:25.302 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5250/favicon.ico, Response status code: 404
2025-06-24 13:02:37.938 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/profile - null null
2025-06-24 13:02:37.943 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.GetProfile (GamalComapnyApp.API)'
2025-06-24 13:02:37.944 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 13:02:37.969 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 13:02:37.974 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.UserResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 13:02:37.977 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.GetProfile (GamalComapnyApp.API) in 30.3663ms
2025-06-24 13:02:37.978 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.GetProfile (GamalComapnyApp.API)'
2025-06-24 13:02:37.980 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/profile - 200 null application/json; charset=utf-8 42.4021ms
2025-06-24 13:03:09.303 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250//uploads//users//user_2_20250624095429.png - null null
2025-06-24 13:03:09.355 +03:00 [INF] Sending file. Request path: '//uploads//users//user_2_20250624095429.png'. Physical path: 'D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp\wwwroot\uploads\users\user_2_20250624095429.png'
2025-06-24 13:03:09.358 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250//uploads//users//user_2_20250624095429.png - ********** image/png 54.9645ms
2025-06-24 13:03:11.264 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - null null
2025-06-24 13:03:11.269 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - 200 16523 application/javascript; charset=utf-8 4.4814ms
2025-06-24 13:03:11.531 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 13:03:11.561 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 30.7036ms
2025-06-24 13:06:13.159 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/2/permissions - null null
2025-06-24 13:06:13.167 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 13:06:13.168 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 13:06:13.169 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 13:06:13.171 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/2/permissions - 401 0 null 13.8434ms
2025-06-24 13:06:16.480 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/2/permissions - null null
2025-06-24 13:06:16.483 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 13:06:16.484 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 13:06:16.486 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 13:06:16.487 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/2/permissions - 401 0 null 7.8784ms
2025-06-24 13:06:36.318 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/2/permissions - null null
2025-06-24 13:06:36.323 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.GetUserPermissions (GamalComapnyApp.API)'
2025-06-24 13:06:36.329 +03:00 [INF] Route matched with {action = "GetUserPermissions", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUserPermissions(Int32) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 13:06:36.377 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[IsActive], [u].[IsDeleted], [u].[ModuleId], [u].[Permission], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserId], [m].[Id], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[IsActive], [m].[IsDeleted], [m].[NameEn], [m].[UpdatedAt], [m].[UpdatedBy]
FROM [UserPermissions] AS [u]
INNER JOIN [Modules] AS [m] ON [u].[ModuleId] = [m].[Id]
WHERE [u].[UserId] = @__userId_0 AND [u].[IsDeleted] = CAST(0 AS bit)
2025-06-24 13:06:36.384 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.UserDto.UserPermissionResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 13:06:36.389 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.GetUserPermissions (GamalComapnyApp.API) in 57.4005ms
2025-06-24 13:06:36.391 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.GetUserPermissions (GamalComapnyApp.API)'
2025-06-24 13:06:36.393 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/2/permissions - 200 null application/json; charset=utf-8 74.2395ms
2025-06-24 13:07:04.737 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/2/permission-summary - null null
2025-06-24 13:07:04.742 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.GetUserPermissionSummary (GamalComapnyApp.API)'
2025-06-24 13:07:04.756 +03:00 [INF] Route matched with {action = "GetUserPermissionSummary", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUserPermissionSummary(Int32) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 13:07:04.799 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[@__userId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__userId_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 13:07:04.808 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.UserPermissionSummaryDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 13:07:04.815 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.GetUserPermissionSummary (GamalComapnyApp.API) in 53.8462ms
2025-06-24 13:07:04.822 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.GetUserPermissionSummary (GamalComapnyApp.API)'
2025-06-24 13:07:04.824 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/2/permission-summary - 200 null application/json; charset=utf-8 86.671ms
2025-06-24 13:07:45.107 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/2/has-permission?module=12 - null null
2025-06-24 13:07:45.130 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.HasPermission (GamalComapnyApp.API)'
2025-06-24 13:07:45.134 +03:00 [INF] Route matched with {action = "HasPermission", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] HasPermission(Int32, System.String, System.String) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 13:07:45.148 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 13:07:45.163 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.HasPermission (GamalComapnyApp.API) in 25.6658ms
2025-06-24 13:07:45.165 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.HasPermission (GamalComapnyApp.API)'
2025-06-24 13:07:45.166 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/2/has-permission?module=12 - 400 null application/problem+json; charset=utf-8 62.6341ms
2025-06-24 13:07:55.180 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/2/has-permission?module=12&permission=Admin - null null
2025-06-24 13:07:55.184 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.HasPermission (GamalComapnyApp.API)'
2025-06-24 13:07:55.186 +03:00 [INF] Route matched with {action = "HasPermission", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] HasPermission(Int32, System.String, System.String) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 13:07:55.230 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[@__userId_0='?' (DbType = Int32), @__moduleName_1='?' (Size = 4000), @__permission_2='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [UserPermissions] AS [u]
        INNER JOIN [Modules] AS [m] ON [u].[ModuleId] = [m].[Id]
        WHERE [u].[UserId] = @__userId_0 AND [m].[NameEn] = @__moduleName_1 AND [u].[Permission] = @__permission_2 AND [u].[IsActive] = CAST(1 AS bit) AND [u].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-24 13:07:55.236 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 13:07:55.239 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.HasPermission (GamalComapnyApp.API) in 50.0436ms
2025-06-24 13:07:55.240 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.HasPermission (GamalComapnyApp.API)'
2025-06-24 13:07:55.242 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/2/has-permission?module=12&permission=Admin - 200 null application/json; charset=utf-8 62.0261ms
2025-06-24 14:33:11.855 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-24 14:33:11.920 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-06-24 14:33:11.949 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-06-24 14:33:31.321 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-24 14:33:31.468 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-24 14:33:31.471 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 14:33:31.472 +03:00 [INF] Hosting environment: Development
2025-06-24 14:33:31.473 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-24 14:34:30.957 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/user/login - application/json 43
2025-06-24 14:34:31.024 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 14:34:31.043 +03:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(GamalComapany.Service.Dtos.UserDto.LoginDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 14:34:32.720 +03:00 [INF] Executed DbCommand (46ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 14:34:32.872 +03:00 [INF] Access token generated for user 2
2025-06-24 14:34:33.040 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-06-24 14:34:33.063 +03:00 [INF] User logged in successfully: Admin
2025-06-24 14:34:33.071 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.LoginResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 14:34:33.116 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API) in 2068.3054ms
2025-06-24 14:34:33.122 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 14:34:33.130 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/user/login - 200 null application/json; charset=utf-8 2174.5079ms
2025-06-24 14:34:49.976 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/user - application/json 89
2025-06-24 14:34:50.025 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.CreateUser (GamalComapnyApp.API)'
2025-06-24 14:34:50.030 +03:00 [INF] Route matched with {action = "CreateUser", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateUser(GamalComapany.Service.Dtos.UserDto.CreateUserDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 14:34:50.109 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__userDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[UserName] = @__userDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
2025-06-24 14:34:50.238 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (Size = 4000), @p6='?' (Size = 4000), @p7='?' (Size = 4000), @p8='?' (DbType = DateTime2), @p9='?' (DbType = Int32), @p10='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Users] ([CreatedAt], [CreatedBy], [FullName], [IsActive], [IsDeleted], [Password], [Phone], [ProfileImage], [UpdatedAt], [UpdatedBy], [UserName])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10);
2025-06-24 14:34:50.245 +03:00 [INF] User created successfully: testuser
2025-06-24 14:34:50.274 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 14:34:50.288 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.UserResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 14:34:50.298 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.CreateUser (GamalComapnyApp.API) in 267.0232ms
2025-06-24 14:34:50.301 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.CreateUser (GamalComapnyApp.API)'
2025-06-24 14:34:50.304 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/user - 200 null application/json; charset=utf-8 327.2521ms
2025-06-24 14:35:24.853 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/user/profile - null null
2025-06-24 14:35:24.866 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.GetProfile (GamalComapnyApp.API)'
2025-06-24 14:35:24.870 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 14:35:24.902 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 14:35:24.907 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.UserResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 14:35:24.910 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.GetProfile (GamalComapnyApp.API) in 38.6342ms
2025-06-24 14:35:24.912 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.GetProfile (GamalComapnyApp.API)'
2025-06-24 14:35:24.913 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/user/profile - 200 null application/json; charset=utf-8 60.1282ms
2025-06-24 14:36:03.400 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger - null null
2025-06-24 14:36:03.406 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger - 301 0 null 5.2577ms
2025-06-24 14:36:03.436 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - null null
2025-06-24 14:36:03.441 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - 404 0 null 5.3679ms
2025-06-24 14:36:03.445 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js, Response status code: 404
2025-06-24 14:36:03.600 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_vs/browserLink - null null
2025-06-24 14:36:03.611 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_vs/browserLink - 404 0 null 11.4945ms
2025-06-24 14:36:03.616 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5250/_vs/browserLink, Response status code: 404
2025-06-24 14:36:03.907 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 14:36:04.008 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 14:36:04.027 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 120.4299ms
2025-06-24 14:36:20.162 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 14:36:20.216 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 54.0011ms
2025-06-24 14:36:20.310 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 14:36:20.330 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 14:36:20.337 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 26.9756ms
2025-06-24 14:36:26.752 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 14:36:26.756 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 4.273ms
2025-06-24 14:36:26.855 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 14:36:26.906 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 14:36:26.912 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 56.9576ms
2025-06-24 14:36:27.752 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 14:36:27.757 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 4.5677ms
2025-06-24 14:36:27.856 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 14:36:27.868 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 14:36:27.876 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 20.1005ms
2025-06-24 14:36:28.304 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 14:36:28.308 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 4.3122ms
2025-06-24 14:36:28.410 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 14:36:28.423 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 14:36:28.429 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 19.447ms
2025-06-24 14:39:37.994 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 14:39:37.998 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 3.4883ms
2025-06-24 14:39:38.302 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 14:39:38.318 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 14:39:38.324 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 22.3211ms
2025-06-24 14:40:53.924 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 14:40:53.928 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 3.7657ms
2025-06-24 14:40:54.062 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 14:40:54.076 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 14:40:54.084 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 21.4677ms
2025-06-24 14:45:36.515 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 14:45:36.519 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 4.2203ms
2025-06-24 14:45:36.823 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 14:45:36.840 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 14:45:36.846 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 22.6767ms
2025-06-24 15:05:50.515 +03:00 [INF] Application is shutting down...
2025-06-24 15:06:31.223 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-24 15:06:31.370 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-24 15:06:31.374 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 15:06:31.375 +03:00 [INF] Hosting environment: Development
2025-06-24 15:06:31.376 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-24 15:06:34.240 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/ - null null
2025-06-24 15:06:34.312 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/ - 404 0 null 72.4838ms
2025-06-24 15:06:34.342 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5250/, Response status code: 404
2025-06-24 15:06:40.370 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 15:06:40.509 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 15:06:40.531 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 160.5173ms
2025-06-24 15:06:53.245 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 15:06:53.276 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 15:06:53.302 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/html; charset=utf-8 56.7482ms
2025-06-24 15:09:39.899 +03:00 [INF] Application is shutting down...
2025-06-24 15:09:49.175 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-24 15:09:49.329 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-24 15:09:49.333 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 15:09:49.335 +03:00 [INF] Hosting environment: Development
2025-06-24 15:09:49.336 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-24 15:09:49.366 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 15:09:49.546 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 15:09:49.569 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 203.9094ms
2025-06-24 15:09:51.275 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 15:09:51.306 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 15:09:51.316 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 40.5956ms
2025-06-24 15:09:56.283 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 15:09:56.312 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 15:09:56.325 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 41.5524ms
2025-06-24 15:09:59.117 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 15:09:59.134 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 15:09:59.156 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/html; charset=utf-8 39.1871ms
2025-06-24 15:14:12.441 +03:00 [INF] Application is shutting down...
2025-06-24 15:14:17.463 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-24 15:14:17.597 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-24 15:14:17.600 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 15:14:17.602 +03:00 [INF] Hosting environment: Development
2025-06-24 15:14:17.603 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-24 15:14:24.865 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/ - null null
2025-06-24 15:14:24.949 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/ - 404 0 null 84.4915ms
2025-06-24 15:14:24.981 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5250/, Response status code: 404
2025-06-24 15:14:37.055 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 15:14:37.200 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 15:14:37.218 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 163.3468ms
2025-06-24 15:27:59.002 +03:00 [INF] Application is shutting down...
