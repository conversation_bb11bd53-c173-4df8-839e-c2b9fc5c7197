2025-06-24 11:29:23.825 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-24 11:29:24.195 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-24 11:29:24.301 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 11:29:24.303 +03:00 [INF] Hosting environment: Development
2025-06-24 11:29:24.304 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-24 11:29:48.304 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 11:29:48.615 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 317.9145ms
2025-06-24 11:29:48.694 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/swagger-ui.css - null null
2025-06-24 11:29:48.696 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/swagger-ui-bundle.js - null null
2025-06-24 11:29:48.703 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.js - null null
2025-06-24 11:29:48.694 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.css - null null
2025-06-24 11:29:48.703 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/swagger-ui-standalone-preset.js - null null
2025-06-24 11:29:48.703 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - null null
2025-06-24 11:29:48.726 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.js - 200 null application/javascript;charset=utf-8 27.2036ms
2025-06-24 11:29:48.753 +03:00 [INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
2025-06-24 11:29:48.757 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - 200 16523 application/javascript; charset=utf-8 53.7301ms
2025-06-24 11:29:48.762 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_vs/browserLink - null null
2025-06-24 11:29:48.764 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.css - 200 202 text/css 69.7286ms
2025-06-24 11:29:48.770 +03:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-06-24 11:29:48.766 +03:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-06-24 11:29:48.828 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/swagger-ui-standalone-preset.js - 200 229223 text/javascript 128.9287ms
2025-06-24 11:29:48.828 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/swagger-ui.css - 200 154949 text/css 136.4819ms
2025-06-24 11:29:48.830 +03:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-06-24 11:29:48.857 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/swagger-ui-bundle.js - 200 1484234 text/javascript 162.7342ms
2025-06-24 11:29:48.880 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_vs/browserLink - 200 null text/javascript; charset=UTF-8 118.3759ms
2025-06-24 11:29:49.137 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 11:29:49.169 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/favicon-32x32.png - null null
2025-06-24 11:29:49.174 +03:00 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-06-24 11:29:49.176 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/favicon-32x32.png - 200 628 image/png 6.7264ms
2025-06-24 11:29:49.323 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 186.3708ms
2025-06-24 11:30:42.878 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 11:30:42.954 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 11:30:42.956 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:30:42.962 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 11:30:42.965 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 401 0 null 118.7828ms
2025-06-24 11:30:44.087 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 11:30:44.094 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 11:30:44.096 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:30:44.097 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 11:30:44.101 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 401 0 null 14.015ms
2025-06-24 11:35:04.111 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-24 11:35:04.172 +03:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-06-24 11:35:04.216 +03:00 [INF] Executed DbCommand (41ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_getapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session', @LockMode = 'Exclusive';
SELECT @result
2025-06-24 11:35:04.301 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
2025-06-24 11:35:04.309 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-24 11:35:04.310 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-06-24 11:35:04.325 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-06-24 11:35:04.335 +03:00 [INF] Applying migration '20250624083344_NewTables'.
2025-06-24 11:35:04.562 +03:00 [INF] Executed DbCommand (109ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @var sysname;
SELECT @var = [d].[name]
FROM [sys].[default_constraints] [d]
INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
WHERE ([d].[parent_object_id] = OBJECT_ID(N'[ItemImages]') AND [c].[name] = N'IsPrimary');
IF @var IS NOT NULL EXEC(N'ALTER TABLE [ItemImages] DROP CONSTRAINT [' + @var + '];');
ALTER TABLE [ItemImages] ALTER COLUMN [IsPrimary] bit NOT NULL;
2025-06-24 11:35:04.582 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [RefreshTokens] (
    [Id] int NOT NULL IDENTITY,
    [Token] nvarchar(max) NOT NULL,
    [UserId] int NOT NULL,
    [ExpiryDate] datetime2 NOT NULL,
    [IsRevoked] bit NOT NULL,
    [RevokedReason] nvarchar(max) NULL,
    [RevokedAt] datetime2 NULL,
    [IpAddress] nvarchar(max) NULL,
    [UserAgent] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] int NULL,
    [UpdatedBy] int NULL,
    [IsDeleted] bit NOT NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_RefreshTokens] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_RefreshTokens_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);
2025-06-24 11:35:04.596 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE [Users] SET [Password] = N'Admin@123'
WHERE [Id] = 1;
SELECT @@ROWCOUNT;
2025-06-24 11:35:04.603 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_RefreshTokens_UserId] ON [RefreshTokens] ([UserId]);
2025-06-24 11:35:04.610 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250624083344_NewTables', N'9.0.5');
2025-06-24 11:35:04.634 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_releaseapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session';
SELECT @result
2025-06-24 11:37:35.559 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-24 11:37:35.829 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-24 11:37:35.898 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 11:37:35.900 +03:00 [INF] Hosting environment: Development
2025-06-24 11:37:35.901 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-24 11:37:40.760 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 11:37:40.993 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 238.1812ms
2025-06-24 11:37:41.032 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - null null
2025-06-24 11:37:41.041 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_vs/browserLink - null null
2025-06-24 11:37:41.045 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - 200 16523 application/javascript; charset=utf-8 13.3981ms
2025-06-24 11:37:41.083 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_vs/browserLink - 200 null text/javascript; charset=UTF-8 42.1737ms
2025-06-24 11:37:41.379 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 11:37:41.564 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 184.8775ms
2025-06-24 11:37:55.181 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 11:37:55.275 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 11:37:55.277 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:37:55.281 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 11:37:55.284 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 401 0 null 103.6515ms
2025-06-24 11:37:56.416 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 11:37:56.426 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 11:37:56.427 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:37:56.429 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 11:37:56.430 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 401 0 null 13.7564ms
2025-06-24 11:38:17.607 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/login - application/json 52
2025-06-24 11:38:17.614 +03:00 [INF] CORS policy execution successful.
2025-06-24 11:38:17.631 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 11:38:17.671 +03:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(GamalComapany.Service.Dtos.UserDto.LoginDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 11:38:19.944 +03:00 [INF] Executed DbCommand (66ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 11:38:20.088 +03:00 [ERR] Error verifying password
System.FormatException: The input is not a valid Base-64 string as it contains a non-base 64 character, more than two padding characters, or an illegal character among the padding characters.
   at System.Convert.FromBase64CharPtr(Char* inputPtr, Int32 inputLength)
   at System.Convert.FromBase64String(String s)
   at GamalComapany.Service.Authentication.PasswordHashingService.VerifyPassword(String password, String hashedPassword) in D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapany.Service\Authentication\PasswordHashingService.cs:line 61
2025-06-24 11:38:20.104 +03:00 [WRN] Login attempt with invalid password for user: Admin
2025-06-24 11:38:20.116 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.LoginResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:38:20.144 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API) in 2465.6519ms
2025-06-24 11:38:20.147 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 11:38:20.159 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/login - 401 null application/json; charset=utf-8 2551.2116ms
2025-06-24 11:39:23.938 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User - application/json 123
2025-06-24 11:39:23.943 +03:00 [INF] CORS policy execution successful.
2025-06-24 11:39:23.947 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 11:39:23.948 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:39:23.949 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 11:39:23.950 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User - 401 0 null 44.6553ms
2025-06-24 11:39:29.957 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User - application/json 123
2025-06-24 11:39:29.960 +03:00 [INF] CORS policy execution successful.
2025-06-24 11:39:29.961 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 11:39:29.963 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:39:29.964 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 11:39:29.966 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User - 401 0 null 8.9191ms
2025-06-24 11:41:14.653 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-24 11:41:14.965 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-24 11:41:15.037 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 11:41:15.038 +03:00 [INF] Hosting environment: Development
2025-06-24 11:41:15.039 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-24 11:41:32.791 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 11:41:33.026 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 240.6584ms
2025-06-24 11:41:33.045 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - null null
2025-06-24 11:41:33.045 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_vs/browserLink - null null
2025-06-24 11:41:33.061 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - 200 16523 application/javascript; charset=utf-8 16.0382ms
2025-06-24 11:41:33.092 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_vs/browserLink - 200 null text/javascript; charset=UTF-8 47.1984ms
2025-06-24 11:41:33.232 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 11:41:33.403 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 170.7773ms
2025-06-24 11:42:15.348 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User - application/json 120
2025-06-24 11:42:15.357 +03:00 [INF] CORS policy execution successful.
2025-06-24 11:42:15.444 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.CreateUser (GamalComapnyApp.API)'
2025-06-24 11:42:15.469 +03:00 [INF] Route matched with {action = "CreateUser", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateUser(GamalComapany.Service.Dtos.UserDto.CreateUserDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 11:42:17.498 +03:00 [INF] Executed DbCommand (51ms) [Parameters=[@__userDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[UserName] = @__userDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
2025-06-24 11:42:17.542 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.UserResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:42:17.576 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.CreateUser (GamalComapnyApp.API) in 2101.5823ms
2025-06-24 11:42:17.579 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.CreateUser (GamalComapnyApp.API)'
2025-06-24 11:42:17.588 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User - 400 null application/json; charset=utf-8 2240.1249ms
2025-06-24 11:42:54.673 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User - application/json 120
2025-06-24 11:42:54.694 +03:00 [INF] CORS policy execution successful.
2025-06-24 11:42:54.735 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.CreateUser (GamalComapnyApp.API)'
2025-06-24 11:42:54.737 +03:00 [INF] Route matched with {action = "CreateUser", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateUser(GamalComapany.Service.Dtos.UserDto.CreateUserDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 11:42:54.805 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[UserName] = @__userDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
2025-06-24 11:42:55.152 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (Size = 4000), @p6='?' (Size = 4000), @p7='?' (Size = 4000), @p8='?' (DbType = DateTime2), @p9='?' (DbType = Int32), @p10='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Users] ([CreatedAt], [CreatedBy], [FullName], [IsActive], [IsDeleted], [Password], [Phone], [ProfileImage], [UpdatedAt], [UpdatedBy], [UserName])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10);
2025-06-24 11:42:55.184 +03:00 [INF] User created successfully: Admin
2025-06-24 11:42:55.310 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 11:42:55.330 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.UserResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:42:55.338 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.CreateUser (GamalComapnyApp.API) in 598.1489ms
2025-06-24 11:42:55.340 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.CreateUser (GamalComapnyApp.API)'
2025-06-24 11:42:55.341 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User - 200 null application/json; charset=utf-8 673.5844ms
2025-06-24 11:43:58.496 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/login - application/json 52
2025-06-24 11:43:58.505 +03:00 [INF] CORS policy execution successful.
2025-06-24 11:43:58.508 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 11:43:58.512 +03:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(GamalComapany.Service.Dtos.UserDto.LoginDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 11:43:58.570 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 11:43:58.713 +03:00 [INF] Access token generated for user 2
2025-06-24 11:43:58.761 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-06-24 11:43:58.770 +03:00 [INF] User logged in successfully: Admin
2025-06-24 11:43:58.772 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.LoginResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:43:58.783 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API) in 266.5947ms
2025-06-24 11:43:58.785 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 11:43:58.787 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/login - 200 null application/json; charset=utf-8 295.4586ms
2025-06-24 11:44:18.768 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 11:44:18.794 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 11:44:18.797 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:44:18.803 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 11:44:18.805 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 401 0 null 36.7278ms
2025-06-24 11:44:20.139 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 11:44:20.143 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 11:44:20.145 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:44:20.146 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 11:44:20.162 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 401 0 null 22.8888ms
2025-06-24 11:44:50.353 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 11:44:50.359 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 11:44:50.360 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:44:50.362 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 11:44:50.363 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 401 0 null 15.5271ms
2025-06-24 11:45:18.321 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 11:45:18.524 +03:00 [WRN] Authorization failed for user 2: Missing permission User:Manage
2025-06-24 11:45:18.526 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:45:18.529 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-06-24 11:45:18.531 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 403 0 null 212.1536ms
2025-06-24 11:46:11.558 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 11:46:11.562 +03:00 [WRN] Authorization failed for user 2: Missing permission User:Manage
2025-06-24 11:46:11.564 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:46:11.565 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-06-24 11:46:11.567 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 403 0 null 59.3793ms
2025-06-24 11:46:42.499 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 11:46:42.502 +03:00 [WRN] Authorization failed for user 2: Missing permission User:Manage
2025-06-24 11:46:42.503 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:46:42.504 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-06-24 11:46:42.519 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 403 0 null 20.0392ms
2025-06-24 11:57:05.128 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-24 11:57:05.504 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-24 11:57:05.621 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 11:57:05.664 +03:00 [INF] Hosting environment: Development
2025-06-24 11:57:05.665 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-24 11:58:01.101 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 11:58:01.369 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 272.9856ms
2025-06-24 11:58:01.393 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_vs/browserLink - null null
2025-06-24 11:58:01.393 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - null null
2025-06-24 11:58:01.400 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - 200 16523 application/javascript; charset=utf-8 7.5158ms
2025-06-24 11:58:01.444 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_vs/browserLink - 200 null text/javascript; charset=UTF-8 50.6036ms
2025-06-24 11:58:01.709 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 11:58:01.867 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 158.3282ms
2025-06-24 11:58:19.021 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/modules - null null
2025-06-24 11:58:19.170 +03:00 [WRN] Authorization failed for user 2: Missing permission User:Manage
2025-06-24 11:58:19.172 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:58:19.175 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-06-24 11:58:19.177 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/modules - 403 0 null 156.5093ms
2025-06-24 11:58:20.609 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/modules - null null
2025-06-24 11:58:20.620 +03:00 [WRN] Authorization failed for user 2: Missing permission User:Manage
2025-06-24 11:58:20.622 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 11:58:20.623 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-06-24 11:58:20.625 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/modules - 403 0 null 16.1287ms
2025-06-24 12:50:09.006 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/logout - null 0
2025-06-24 12:50:09.014 +03:00 [INF] CORS policy execution successful.
2025-06-24 12:50:09.020 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 09:50:09 ص'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-24 12:50:09.026 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 09:50:09 ص'.
2025-06-24 12:50:09.031 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 09:50:09 ص'.
2025-06-24 12:50:09.035 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-24 12:50:09.041 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 12:50:09.044 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/logout - 401 0 null 38.033ms
2025-06-24 12:50:12.697 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/logout - null 0
2025-06-24 12:50:12.701 +03:00 [INF] CORS policy execution successful.
2025-06-24 12:50:12.703 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 09:50:12 ص'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-24 12:50:12.706 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 09:50:12 ص'.
2025-06-24 12:50:12.723 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 09:50:12 ص'.
2025-06-24 12:50:12.726 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-24 12:50:12.727 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 12:50:12.728 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/logout - 401 0 null 31.4178ms
2025-06-24 12:50:56.963 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/login - application/json 52
2025-06-24 12:50:56.967 +03:00 [INF] CORS policy execution successful.
2025-06-24 12:50:56.971 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 12:50:56.997 +03:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(GamalComapany.Service.Dtos.UserDto.LoginDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 12:51:00.170 +03:00 [INF] Executed DbCommand (101ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 12:51:00.355 +03:00 [INF] Access token generated for user 2
2025-06-24 12:51:00.581 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-06-24 12:51:00.611 +03:00 [INF] User logged in successfully: Admin
2025-06-24 12:51:00.620 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.LoginResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 12:51:00.653 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API) in 3649.5709ms
2025-06-24 12:51:00.656 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 12:51:00.665 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/login - 200 null application/json; charset=utf-8 3704.9677ms
2025-06-24 12:51:43.494 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 12:51:43.524 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.GetAllUsers (GamalComapnyApp.API)'
2025-06-24 12:51:43.529 +03:00 [INF] Route matched with {action = "GetAllUsers", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers() on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 12:51:43.620 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
WHERE [u].[IsDeleted] = CAST(0 AS bit)
ORDER BY [u].[Id], [s].[Id]
2025-06-24 12:51:43.641 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.UserDto.UserResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 12:51:43.655 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.GetAllUsers (GamalComapnyApp.API) in 121.4794ms
2025-06-24 12:51:43.658 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.GetAllUsers (GamalComapnyApp.API)'
2025-06-24 12:51:43.660 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 200 null application/json; charset=utf-8 170.0011ms
2025-06-24 12:52:01.345 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/modules - null null
2025-06-24 12:52:01.349 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.GetAllModules (GamalComapnyApp.API)'
2025-06-24 12:52:01.356 +03:00 [INF] Route matched with {action = "GetAllModules", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllModules() on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 12:52:01.424 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[Id], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[IsActive], [m].[IsDeleted], [m].[NameEn], [m].[UpdatedAt], [m].[UpdatedBy], [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[IsActive], [u].[IsDeleted], [u].[ModuleId], [u].[Permission], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserId]
FROM [Modules] AS [m]
LEFT JOIN [UserPermissions] AS [u] ON [m].[Id] = [u].[ModuleId]
WHERE [m].[IsDeleted] = CAST(0 AS bit)
ORDER BY [m].[Id]
2025-06-24 12:52:01.435 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.UserDto.ModuleResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 12:52:01.440 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.GetAllModules (GamalComapnyApp.API) in 69.8558ms
2025-06-24 12:52:01.443 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.GetAllModules (GamalComapnyApp.API)'
2025-06-24 12:52:01.445 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/modules - 200 null application/json; charset=utf-8 99.8282ms
2025-06-24 12:52:24.779 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Categroy - null null
2025-06-24 12:52:24.803 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.CategroyController.GetCategory (GamalComapnyApp.API)'
2025-06-24 12:52:24.810 +03:00 [INF] Route matched with {action = "GetCategory", controller = "Categroy"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCategory() on controller GamalComapnyApp.API.Controllers.CategroyController (GamalComapnyApp.API).
2025-06-24 12:52:24.831 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [i].[Id], [i].[CategoryTypeId], [i].[Code], [i].[CreatedAt], [i].[CreatedBy], [i].[Description], [i].[ImageUrl], [i].[IsActive], [i].[IsDeleted], [i].[NameEn], [i].[ParentCategoryId], [i].[SortOrder], [i].[Symbol], [i].[UpdatedAt], [i].[UpdatedBy]
FROM [ItemCategories] AS [i]
2025-06-24 12:52:24.834 +03:00 [INF] Executing NotFoundObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalCompany.Data.Models.ItemCategory, GamalCompany.Data, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 12:52:24.842 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.CategroyController.GetCategory (GamalComapnyApp.API) in 28.7744ms
2025-06-24 12:52:24.844 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.CategroyController.GetCategory (GamalComapnyApp.API)'
2025-06-24 12:52:24.848 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Categroy - 404 null application/json; charset=utf-8 73.2456ms
2025-06-24 12:54:29.235 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/Image/profile - multipart/form-data; boundary=----WebKitFormBoundary1jPpY7ToeMLjEJZX 338675
2025-06-24 12:54:29.270 +03:00 [INF] CORS policy execution successful.
2025-06-24 12:54:29.275 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.ImageController.UploadProfileImage (GamalComapnyApp.API)'
2025-06-24 12:54:29.287 +03:00 [INF] Route matched with {action = "UploadProfileImage", controller = "Image"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] UploadProfileImage(Microsoft.AspNetCore.Http.IFormFile) on controller GamalComapnyApp.API.Controllers.ImageController (GamalComapnyApp.API).
2025-06-24 12:54:29.514 +03:00 [INF] User profile images deleted for user: 2
2025-06-24 12:54:29.588 +03:00 [WRN] Could not optimize image: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp\wwwroot\uploads\users\user_2_20250624095429.png
System.IO.IOException: The process cannot access the file 'D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp\wwwroot\uploads\users\user_2_20250624095429.png' because it is being used by another process.
   at Microsoft.Win32.SafeHandles.SafeFileHandle.CreateFile(String fullPath, FileMode mode, FileAccess access, FileShare share, FileOptions options)
   at Microsoft.Win32.SafeHandles.SafeFileHandle.Open(String fullPath, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Nullable`1 unixCreateMode)
   at System.IO.Strategies.OSFileStreamStrategy..ctor(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Nullable`1 unixCreateMode)
   at System.IO.Strategies.FileStreamHelpers.ChooseStrategyCore(String path, FileMode mode, FileAccess access, FileShare share, FileOptions options, Int64 preallocationSize, Nullable`1 unixCreateMode)
   at System.IO.FileStream..ctor(String path, FileStreamOptions options)
   at System.IO.File.Open(String path, FileStreamOptions options)
   at SixLabors.ImageSharp.IO.LocalFileSystem.OpenReadAsynchronous(String path)
   at SixLabors.ImageSharp.Image.LoadAsync(DecoderOptions options, String path, CancellationToken cancellationToken)
   at GamalComapany.Service.Services.ImageService.OptimizeImageAsync(String filePath) in D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapany.Service\Services\ImageService.cs:line 332
2025-06-24 12:54:29.608 +03:00 [INF] Image uploaded successfully: uploads/users/user_2_20250624095429.png
2025-06-24 12:54:29.610 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType0`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 12:54:29.615 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.ImageController.UploadProfileImage (GamalComapnyApp.API) in 325.6241ms
2025-06-24 12:54:29.618 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.ImageController.UploadProfileImage (GamalComapnyApp.API)'
2025-06-24 12:54:29.621 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/Image/profile - 200 null application/json; charset=utf-8 385.7389ms
2025-06-24 12:57:26.317 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User - null null
2025-06-24 12:57:26.322 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.GetAllUsers (GamalComapnyApp.API)'
2025-06-24 12:57:26.323 +03:00 [INF] Route matched with {action = "GetAllUsers", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers() on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 12:57:26.381 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM [Users] AS [u]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u].[Id] = [s].[UserId]
WHERE [u].[IsDeleted] = CAST(0 AS bit)
ORDER BY [u].[Id], [s].[Id]
2025-06-24 12:57:26.387 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.UserDto.UserResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 12:57:26.389 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.GetAllUsers (GamalComapnyApp.API) in 64.2847ms
2025-06-24 12:57:26.390 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.GetAllUsers (GamalComapnyApp.API)'
2025-06-24 12:57:26.396 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User - 200 null application/json; charset=utf-8 78.8411ms
2025-06-24 12:58:40.497 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/profile - null null
2025-06-24 12:58:40.529 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.GetProfile (GamalComapnyApp.API)'
2025-06-24 12:58:40.539 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 12:58:40.583 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 12:58:40.593 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.UserResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 12:58:40.600 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.GetProfile (GamalComapnyApp.API) in 58.2852ms
2025-06-24 12:58:40.601 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.GetProfile (GamalComapnyApp.API)'
2025-06-24 12:58:40.603 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/profile - 200 null application/json; charset=utf-8 143.6198ms
2025-06-24 13:01:47.988 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/uploads/users/user_2_20250624095429 - null null
2025-06-24 13:01:48.013 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/uploads/users/user_2_20250624095429 - 404 0 null 28.1538ms
2025-06-24 13:01:48.018 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5250/uploads/users/user_2_20250624095429, Response status code: 404
2025-06-24 13:02:24.868 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/uploads/users/user_2_20250624095429.png - null null
2025-06-24 13:02:25.192 +03:00 [INF] Sending file. Request path: '/uploads/users/user_2_20250624095429.png'. Physical path: 'D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp\wwwroot\uploads\users\user_2_20250624095429.png'
2025-06-24 13:02:25.194 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/uploads/users/user_2_20250624095429.png - ********** image/png 326.1658ms
2025-06-24 13:02:25.296 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/favicon.ico - null null
2025-06-24 13:02:25.299 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/favicon.ico - 404 0 null 3.5629ms
2025-06-24 13:02:25.302 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5250/favicon.ico, Response status code: 404
2025-06-24 13:02:37.938 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/profile - null null
2025-06-24 13:02:37.943 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.GetProfile (GamalComapnyApp.API)'
2025-06-24 13:02:37.944 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 13:02:37.969 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 13:02:37.974 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.UserResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 13:02:37.977 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.GetProfile (GamalComapnyApp.API) in 30.3663ms
2025-06-24 13:02:37.978 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.GetProfile (GamalComapnyApp.API)'
2025-06-24 13:02:37.980 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/profile - 200 null application/json; charset=utf-8 42.4021ms
2025-06-24 13:03:09.303 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250//uploads//users//user_2_20250624095429.png - null null
2025-06-24 13:03:09.355 +03:00 [INF] Sending file. Request path: '//uploads//users//user_2_20250624095429.png'. Physical path: 'D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp\wwwroot\uploads\users\user_2_20250624095429.png'
2025-06-24 13:03:09.358 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250//uploads//users//user_2_20250624095429.png - ********** image/png 54.9645ms
2025-06-24 13:03:11.264 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - null null
2025-06-24 13:03:11.269 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - 200 16523 application/javascript; charset=utf-8 4.4814ms
2025-06-24 13:03:11.531 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 13:03:11.561 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 30.7036ms
2025-06-24 13:06:13.159 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/2/permissions - null null
2025-06-24 13:06:13.167 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 13:06:13.168 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 13:06:13.169 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 13:06:13.171 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/2/permissions - 401 0 null 13.8434ms
2025-06-24 13:06:16.480 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/2/permissions - null null
2025-06-24 13:06:16.483 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 13:06:16.484 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 13:06:16.486 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 13:06:16.487 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/2/permissions - 401 0 null 7.8784ms
2025-06-24 13:06:36.318 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/2/permissions - null null
2025-06-24 13:06:36.323 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.GetUserPermissions (GamalComapnyApp.API)'
2025-06-24 13:06:36.329 +03:00 [INF] Route matched with {action = "GetUserPermissions", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUserPermissions(Int32) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 13:06:36.377 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[IsActive], [u].[IsDeleted], [u].[ModuleId], [u].[Permission], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserId], [m].[Id], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[IsActive], [m].[IsDeleted], [m].[NameEn], [m].[UpdatedAt], [m].[UpdatedBy]
FROM [UserPermissions] AS [u]
INNER JOIN [Modules] AS [m] ON [u].[ModuleId] = [m].[Id]
WHERE [u].[UserId] = @__userId_0 AND [u].[IsDeleted] = CAST(0 AS bit)
2025-06-24 13:06:36.384 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.UserDto.UserPermissionResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 13:06:36.389 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.GetUserPermissions (GamalComapnyApp.API) in 57.4005ms
2025-06-24 13:06:36.391 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.GetUserPermissions (GamalComapnyApp.API)'
2025-06-24 13:06:36.393 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/2/permissions - 200 null application/json; charset=utf-8 74.2395ms
2025-06-24 13:07:04.737 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/2/permission-summary - null null
2025-06-24 13:07:04.742 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.GetUserPermissionSummary (GamalComapnyApp.API)'
2025-06-24 13:07:04.756 +03:00 [INF] Route matched with {action = "GetUserPermissionSummary", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUserPermissionSummary(Int32) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 13:07:04.799 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[@__userId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__userId_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 13:07:04.808 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.UserPermissionSummaryDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 13:07:04.815 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.GetUserPermissionSummary (GamalComapnyApp.API) in 53.8462ms
2025-06-24 13:07:04.822 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.GetUserPermissionSummary (GamalComapnyApp.API)'
2025-06-24 13:07:04.824 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/2/permission-summary - 200 null application/json; charset=utf-8 86.671ms
2025-06-24 13:07:45.107 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/2/has-permission?module=12 - null null
2025-06-24 13:07:45.130 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.HasPermission (GamalComapnyApp.API)'
2025-06-24 13:07:45.134 +03:00 [INF] Route matched with {action = "HasPermission", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] HasPermission(Int32, System.String, System.String) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 13:07:45.148 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 13:07:45.163 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.HasPermission (GamalComapnyApp.API) in 25.6658ms
2025-06-24 13:07:45.165 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.HasPermission (GamalComapnyApp.API)'
2025-06-24 13:07:45.166 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/2/has-permission?module=12 - 400 null application/problem+json; charset=utf-8 62.6341ms
2025-06-24 13:07:55.180 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/User/2/has-permission?module=12&permission=Admin - null null
2025-06-24 13:07:55.184 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.HasPermission (GamalComapnyApp.API)'
2025-06-24 13:07:55.186 +03:00 [INF] Route matched with {action = "HasPermission", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] HasPermission(Int32, System.String, System.String) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 13:07:55.230 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[@__userId_0='?' (DbType = Int32), @__moduleName_1='?' (Size = 4000), @__permission_2='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [UserPermissions] AS [u]
        INNER JOIN [Modules] AS [m] ON [u].[ModuleId] = [m].[Id]
        WHERE [u].[UserId] = @__userId_0 AND [m].[NameEn] = @__moduleName_1 AND [u].[Permission] = @__permission_2 AND [u].[IsActive] = CAST(1 AS bit) AND [u].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-24 13:07:55.236 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 13:07:55.239 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.HasPermission (GamalComapnyApp.API) in 50.0436ms
2025-06-24 13:07:55.240 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.HasPermission (GamalComapnyApp.API)'
2025-06-24 13:07:55.242 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/User/2/has-permission?module=12&permission=Admin - 200 null application/json; charset=utf-8 62.0261ms
2025-06-24 14:33:11.855 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-24 14:33:11.920 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-06-24 14:33:11.949 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-06-24 14:33:31.321 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-24 14:33:31.468 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-24 14:33:31.471 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 14:33:31.472 +03:00 [INF] Hosting environment: Development
2025-06-24 14:33:31.473 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-24 14:34:30.957 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/user/login - application/json 43
2025-06-24 14:34:31.024 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 14:34:31.043 +03:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(GamalComapany.Service.Dtos.UserDto.LoginDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 14:34:32.720 +03:00 [INF] Executed DbCommand (46ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 14:34:32.872 +03:00 [INF] Access token generated for user 2
2025-06-24 14:34:33.040 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-06-24 14:34:33.063 +03:00 [INF] User logged in successfully: Admin
2025-06-24 14:34:33.071 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.LoginResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 14:34:33.116 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API) in 2068.3054ms
2025-06-24 14:34:33.122 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 14:34:33.130 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/user/login - 200 null application/json; charset=utf-8 2174.5079ms
2025-06-24 14:34:49.976 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/user - application/json 89
2025-06-24 14:34:50.025 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.CreateUser (GamalComapnyApp.API)'
2025-06-24 14:34:50.030 +03:00 [INF] Route matched with {action = "CreateUser", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateUser(GamalComapany.Service.Dtos.UserDto.CreateUserDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 14:34:50.109 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__userDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[UserName] = @__userDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
2025-06-24 14:34:50.238 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (Size = 4000), @p6='?' (Size = 4000), @p7='?' (Size = 4000), @p8='?' (DbType = DateTime2), @p9='?' (DbType = Int32), @p10='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Users] ([CreatedAt], [CreatedBy], [FullName], [IsActive], [IsDeleted], [Password], [Phone], [ProfileImage], [UpdatedAt], [UpdatedBy], [UserName])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10);
2025-06-24 14:34:50.245 +03:00 [INF] User created successfully: testuser
2025-06-24 14:34:50.274 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 14:34:50.288 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.UserResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 14:34:50.298 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.CreateUser (GamalComapnyApp.API) in 267.0232ms
2025-06-24 14:34:50.301 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.CreateUser (GamalComapnyApp.API)'
2025-06-24 14:34:50.304 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/user - 200 null application/json; charset=utf-8 327.2521ms
2025-06-24 14:35:24.853 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/user/profile - null null
2025-06-24 14:35:24.866 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.GetProfile (GamalComapnyApp.API)'
2025-06-24 14:35:24.870 +03:00 [INF] Route matched with {action = "GetProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile() on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 14:35:24.902 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 14:35:24.907 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.UserResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 14:35:24.910 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.GetProfile (GamalComapnyApp.API) in 38.6342ms
2025-06-24 14:35:24.912 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.GetProfile (GamalComapnyApp.API)'
2025-06-24 14:35:24.913 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/user/profile - 200 null application/json; charset=utf-8 60.1282ms
2025-06-24 14:36:03.400 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger - null null
2025-06-24 14:36:03.406 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger - 301 0 null 5.2577ms
2025-06-24 14:36:03.436 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - null null
2025-06-24 14:36:03.441 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - 404 0 null 5.3679ms
2025-06-24 14:36:03.445 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js, Response status code: 404
2025-06-24 14:36:03.600 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_vs/browserLink - null null
2025-06-24 14:36:03.611 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_vs/browserLink - 404 0 null 11.4945ms
2025-06-24 14:36:03.616 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5250/_vs/browserLink, Response status code: 404
2025-06-24 14:36:03.907 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 14:36:04.008 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 14:36:04.027 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 120.4299ms
2025-06-24 14:36:20.162 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 14:36:20.216 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 54.0011ms
2025-06-24 14:36:20.310 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 14:36:20.330 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 14:36:20.337 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 26.9756ms
2025-06-24 14:36:26.752 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 14:36:26.756 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 4.273ms
2025-06-24 14:36:26.855 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 14:36:26.906 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 14:36:26.912 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 56.9576ms
2025-06-24 14:36:27.752 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 14:36:27.757 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 4.5677ms
2025-06-24 14:36:27.856 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 14:36:27.868 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 14:36:27.876 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 20.1005ms
2025-06-24 14:36:28.304 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 14:36:28.308 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 4.3122ms
2025-06-24 14:36:28.410 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 14:36:28.423 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 14:36:28.429 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 19.447ms
2025-06-24 14:39:37.994 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 14:39:37.998 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 3.4883ms
2025-06-24 14:39:38.302 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 14:39:38.318 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 14:39:38.324 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 22.3211ms
2025-06-24 14:40:53.924 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 14:40:53.928 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 3.7657ms
2025-06-24 14:40:54.062 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 14:40:54.076 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 14:40:54.084 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 21.4677ms
2025-06-24 14:45:36.515 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 14:45:36.519 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 4.2203ms
2025-06-24 14:45:36.823 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 14:45:36.840 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 14:45:36.846 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 22.6767ms
2025-06-24 15:05:50.515 +03:00 [INF] Application is shutting down...
2025-06-24 15:06:31.223 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-24 15:06:31.370 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-24 15:06:31.374 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 15:06:31.375 +03:00 [INF] Hosting environment: Development
2025-06-24 15:06:31.376 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-24 15:06:34.240 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/ - null null
2025-06-24 15:06:34.312 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/ - 404 0 null 72.4838ms
2025-06-24 15:06:34.342 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5250/, Response status code: 404
2025-06-24 15:06:40.370 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 15:06:40.509 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 15:06:40.531 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 160.5173ms
2025-06-24 15:06:53.245 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 15:06:53.276 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 15:06:53.302 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/html; charset=utf-8 56.7482ms
2025-06-24 15:09:39.899 +03:00 [INF] Application is shutting down...
2025-06-24 15:09:49.175 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-24 15:09:49.329 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-24 15:09:49.333 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 15:09:49.335 +03:00 [INF] Hosting environment: Development
2025-06-24 15:09:49.336 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-24 15:09:49.366 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 15:09:49.546 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 15:09:49.569 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 203.9094ms
2025-06-24 15:09:51.275 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 15:09:51.306 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 15:09:51.316 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 40.5956ms
2025-06-24 15:09:56.283 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 15:09:56.312 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 15:09:56.325 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 41.5524ms
2025-06-24 15:09:59.117 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 15:09:59.134 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 15:09:59.156 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/html; charset=utf-8 39.1871ms
2025-06-24 15:14:12.441 +03:00 [INF] Application is shutting down...
2025-06-24 15:14:17.463 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-24 15:14:17.597 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-24 15:14:17.600 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 15:14:17.602 +03:00 [INF] Hosting environment: Development
2025-06-24 15:14:17.603 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-24 15:14:24.865 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/ - null null
2025-06-24 15:14:24.949 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/ - 404 0 null 84.4915ms
2025-06-24 15:14:24.981 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5250/, Response status code: 404
2025-06-24 15:14:37.055 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 15:14:37.200 +03:00 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API). See inner exception
 ---> Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Error reading parameter(s) for action GamalComapnyApp.API.Controllers.UserController.UploadProfileImage (GamalComapnyApp.API) as [FromForm] attribute used with IFormFile. Please refer to https://github.com/domaindrivendev/Swashbuckle.AspNetCore/tree/master/docs/configure-and-customize-swaggergen.md#handle-forms-and-file-uploads for more information
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parameterGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   --- End of inner exception stack trace ---
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 15:14:37.218 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 163.3468ms
2025-06-24 15:27:59.002 +03:00 [INF] Application is shutting down...
2025-06-24 15:28:10.549 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-24 15:28:10.695 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-24 15:28:10.698 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 15:28:10.700 +03:00 [INF] Hosting environment: Development
2025-06-24 15:28:10.701 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-24 15:28:17.013 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 15:28:17.235 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 224.209ms
2025-06-24 15:28:55.646 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/1/upload-image - multipart/form-data; boundary=----WebKitFormBoundaryFtEnA6RDLR85okqx 179122
2025-06-24 15:28:55.666 +03:00 [INF] CORS policy execution successful.
2025-06-24 15:28:55.697 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 15:28:55.699 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 15:28:55.702 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 15:28:55.705 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/1/upload-image - 401 0 null 59.4891ms
2025-06-24 15:29:10.626 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/1/upload-image - multipart/form-data; boundary=----WebKitFormBoundarypgEb2te9iGCAxxxI 179122
2025-06-24 15:29:10.634 +03:00 [INF] CORS policy execution successful.
2025-06-24 15:29:10.706 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:10 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-24 15:29:10.721 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:10 م'.
2025-06-24 15:29:10.726 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:10 م'.
2025-06-24 15:29:10.730 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 15:29:10.731 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 15:29:10.735 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 15:29:10.737 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/1/upload-image - 401 0 null 111.1222ms
2025-06-24 15:29:14.486 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/1/upload-image - multipart/form-data; boundary=----WebKitFormBoundary83n6aouP0aGs1yBp 179122
2025-06-24 15:29:14.489 +03:00 [INF] CORS policy execution successful.
2025-06-24 15:29:14.491 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:14 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-24 15:29:14.508 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:14 م'.
2025-06-24 15:29:14.513 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:14 م'.
2025-06-24 15:29:14.517 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 15:29:14.518 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 15:29:14.519 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 15:29:14.521 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/1/upload-image - 401 0 null 34.8413ms
2025-06-24 15:29:15.544 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/1/upload-image - multipart/form-data; boundary=----WebKitFormBoundary9Czxc7DCfskVjiBK 179122
2025-06-24 15:29:15.548 +03:00 [INF] CORS policy execution successful.
2025-06-24 15:29:15.549 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:15 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-24 15:29:15.555 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:15 م'.
2025-06-24 15:29:15.556 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:15 م'.
2025-06-24 15:29:15.557 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 15:29:15.558 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 15:29:15.559 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 15:29:15.560 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/1/upload-image - 401 0 null 16.3378ms
2025-06-24 15:29:15.791 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/1/upload-image - multipart/form-data; boundary=----WebKitFormBoundary0XNZZIfsmXjRjDne 179122
2025-06-24 15:29:15.794 +03:00 [INF] CORS policy execution successful.
2025-06-24 15:29:15.795 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:15 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-24 15:29:15.798 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:15 م'.
2025-06-24 15:29:15.799 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:15 م'.
2025-06-24 15:29:15.800 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 15:29:15.801 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 15:29:15.802 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 15:29:15.804 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/1/upload-image - 401 0 null 12.4666ms
2025-06-24 15:29:16.016 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/1/upload-image - multipart/form-data; boundary=----WebKitFormBoundarySC6yY8HyVqlGU7uQ 179122
2025-06-24 15:29:16.020 +03:00 [INF] CORS policy execution successful.
2025-06-24 15:29:16.021 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:16 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-24 15:29:16.024 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:16 م'.
2025-06-24 15:29:16.025 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:16 م'.
2025-06-24 15:29:16.027 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 15:29:16.029 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 15:29:16.030 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 15:29:16.032 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/1/upload-image - 401 0 null 15.3527ms
2025-06-24 15:29:16.219 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/1/upload-image - multipart/form-data; boundary=----WebKitFormBoundaryCuoU793hUcGNk59B 179122
2025-06-24 15:29:16.222 +03:00 [INF] CORS policy execution successful.
2025-06-24 15:29:16.223 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:16 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-24 15:29:16.226 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:16 م'.
2025-06-24 15:29:16.228 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:16 م'.
2025-06-24 15:29:16.229 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 15:29:16.230 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 15:29:16.232 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 15:29:16.234 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/1/upload-image - 401 0 null 14.7576ms
2025-06-24 15:29:16.426 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/1/upload-image - multipart/form-data; boundary=----WebKitFormBoundaryOF6HBB0yu1Jax9hR 179122
2025-06-24 15:29:16.429 +03:00 [INF] CORS policy execution successful.
2025-06-24 15:29:16.430 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:16 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-24 15:29:16.433 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:16 م'.
2025-06-24 15:29:16.434 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:16 م'.
2025-06-24 15:29:16.436 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 15:29:16.437 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 15:29:16.438 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 15:29:16.440 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/1/upload-image - 401 0 null 14.2688ms
2025-06-24 15:29:16.642 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/1/upload-image - multipart/form-data; boundary=----WebKitFormBoundaryx5hD5Zldhewe93u7 179122
2025-06-24 15:29:16.646 +03:00 [INF] CORS policy execution successful.
2025-06-24 15:29:16.647 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:16 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-24 15:29:16.650 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:16 م'.
2025-06-24 15:29:16.651 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:16 م'.
2025-06-24 15:29:16.652 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 15:29:16.654 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 15:29:16.655 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 15:29:16.657 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/1/upload-image - 401 0 null 14.7242ms
2025-06-24 15:29:16.842 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/1/upload-image - multipart/form-data; boundary=----WebKitFormBoundaryJyJ7obnlpsrs18Zj 179122
2025-06-24 15:29:16.846 +03:00 [INF] CORS policy execution successful.
2025-06-24 15:29:16.847 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:16 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-24 15:29:16.850 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:16 م'.
2025-06-24 15:29:16.851 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 09:43:58 ص', Current time (UTC): '24/06/2025 12:29:16 م'.
2025-06-24 15:29:16.852 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 15:29:16.853 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 15:29:16.855 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 15:29:16.856 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/1/upload-image - 401 0 null 13.7579ms
2025-06-24 15:29:18.907 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 15:29:18.936 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 28.4278ms
2025-06-24 15:29:58.831 +03:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5250/api/User/profile - multipart/form-data; boundary=----WebKitFormBoundaryExczwqlAXxDJKuDG 179414
2025-06-24 15:29:58.835 +03:00 [INF] CORS policy execution successful.
2025-06-24 15:29:58.837 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-24 15:29:58.838 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 15:29:58.855 +03:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5250/api/User/profile - 401 0 null 8.3125ms
2025-06-24 15:30:03.426 +03:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5250/api/User/profile - multipart/form-data; boundary=----WebKitFormBoundaryYRL1m4dNa7IabKIr 179414
2025-06-24 15:30:03.429 +03:00 [INF] CORS policy execution successful.
2025-06-24 15:30:03.430 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-24 15:30:03.431 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 15:30:03.436 +03:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5250/api/User/profile - 401 0 null 10.209ms
2025-06-24 15:30:11.644 +03:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5250/api/User/profile - multipart/form-data; boundary=----WebKitFormBoundary9XD6KkpeU2qYNSEO 179414
2025-06-24 15:30:11.648 +03:00 [INF] CORS policy execution successful.
2025-06-24 15:30:11.648 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-24 15:30:11.649 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 15:30:11.651 +03:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5250/api/User/profile - 401 0 null 6.6936ms
2025-06-24 15:31:32.337 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/login - application/json 52
2025-06-24 15:31:32.340 +03:00 [INF] CORS policy execution successful.
2025-06-24 15:31:32.343 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 15:31:32.370 +03:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(GamalComapany.Service.Dtos.UserDto.LoginDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 15:31:34.051 +03:00 [INF] Executed DbCommand (39ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 15:31:34.193 +03:00 [INF] Access token generated for user 2
2025-06-24 15:31:34.362 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-06-24 15:31:34.389 +03:00 [INF] User logged in successfully: Admin
2025-06-24 15:31:34.395 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.LoginResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 15:31:34.438 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API) in 2063.1673ms
2025-06-24 15:31:34.441 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 15:31:34.447 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/login - 200 null application/json; charset=utf-8 2110.3548ms
2025-06-24 15:32:01.599 +03:00 [INF] Request starting HTTP/1.1 PUT http://localhost:5250/api/User/profile - multipart/form-data; boundary=----WebKitFormBoundaryuFbA6OvcNJdTCbnO 179414
2025-06-24 15:32:01.603 +03:00 [INF] CORS policy execution successful.
2025-06-24 15:32:01.637 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.UpdateProfile (GamalComapnyApp.API)'
2025-06-24 15:32:01.644 +03:00 [INF] Route matched with {action = "UpdateProfile", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] UpdateProfile(GamalComapany.Service.Dtos.UserDto.UpdateUserProfileDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 15:32:01.773 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-06-24 15:32:01.807 +03:00 [INF] Profile image uploaded successfully for user 2: uploads/profile-images/user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58.jpg
2025-06-24 15:32:01.832 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@p9='?' (DbType = Int32), @p0='?' (Size = 4000), @p1='?' (DbType = Boolean), @p2='?' (DbType = Boolean), @p3='?' (Size = 4000), @p4='?' (Size = 4000), @p5='?' (Size = 4000), @p6='?' (DbType = DateTime2), @p7='?' (DbType = Int32), @p8='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [FullName] = @p0, [IsActive] = @p1, [IsDeleted] = @p2, [Password] = @p3, [Phone] = @p4, [ProfileImage] = @p5, [UpdatedAt] = @p6, [UpdatedBy] = @p7, [UserName] = @p8
OUTPUT 1
WHERE [Id] = @p9;
2025-06-24 15:32:01.840 +03:00 [INF] User profile updated successfully: 2
2025-06-24 15:32:01.869 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[Id] = @__id_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 15:32:01.883 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.UserResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 15:32:01.897 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.UpdateProfile (GamalComapnyApp.API) in 250.1414ms
2025-06-24 15:32:01.899 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.UpdateProfile (GamalComapnyApp.API)'
2025-06-24 15:32:01.902 +03:00 [INF] Request finished HTTP/1.1 PUT http://localhost:5250/api/User/profile - 200 null application/json; charset=utf-8 303.0411ms
2025-06-24 15:32:44.924 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/uploads/profile-images/user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58.jp - null null
2025-06-24 15:32:44.929 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/uploads/profile-images/user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58.jp - 404 0 null 4.1884ms
2025-06-24 15:32:44.933 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5250/uploads/profile-images/user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58.jp, Response status code: 404
2025-06-24 15:32:54.592 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/uploads/profile-images/user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58.jpg - null null
2025-06-24 15:32:54.617 +03:00 [INF] Sending file. Request path: '/uploads/profile-images/user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58.jpg'. Physical path: 'D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp\wwwroot\uploads\profile-images\user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58.jpg'
2025-06-24 15:32:54.622 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/uploads/profile-images/user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58.jpg - ********** image/jpeg 30.0205ms
2025-06-24 15:33:12.448 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/uploads/profile-images/user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58.jpg - null null
2025-06-24 15:33:12.453 +03:00 [INF] The file /uploads/profile-images/user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58.jpg was not modified
2025-06-24 15:33:12.455 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/uploads/profile-images/user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58.jpg - 304 null image/jpeg 6.2719ms
2025-06-24 15:34:19.675 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner - null null
2025-06-24 15:34:19.680 +03:00 [WRN] Authorization failed for user 2: Missing permission Partner:Read
2025-06-24 15:34:19.681 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 15:34:19.683 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-06-24 15:34:19.685 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner - 403 0 null 10.7313ms
2025-06-24 15:34:20.758 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner - null null
2025-06-24 15:34:20.761 +03:00 [WRN] Authorization failed for user 2: Missing permission Partner:Read
2025-06-24 15:34:20.763 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 15:34:20.763 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-06-24 15:34:20.765 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner - 403 0 null 6.4672ms
2025-06-24 15:37:00.849 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner/capital-calculation - null null
2025-06-24 15:37:00.852 +03:00 [WRN] Authorization failed for user 2: Missing permission Partner:Read
2025-06-24 15:37:00.853 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 15:37:00.854 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-06-24 15:37:00.870 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner/capital-calculation - 403 0 null 20.838ms
2025-06-24 15:37:44.404 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/Partner/recalculate-shares - null 0
2025-06-24 15:37:44.408 +03:00 [INF] CORS policy execution successful.
2025-06-24 15:37:44.409 +03:00 [WRN] Authorization failed for user 2: Missing permission Partner:Write
2025-06-24 15:37:44.411 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 15:37:44.412 +03:00 [INF] AuthenticationScheme: Bearer was forbidden.
2025-06-24 15:37:44.413 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/Partner/recalculate-shares - 403 0 null 9.4389ms
2025-06-24 15:57:23.546 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 15:57:23.597 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 50.5344ms
2025-06-24 15:57:23.917 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 15:57:23.942 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 24.3029ms
2025-06-24 15:57:45.114 +03:00 [INF] Application is shutting down...
2025-06-24 16:19:29.760 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-24 16:19:29.903 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-24 16:19:29.908 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 16:19:29.910 +03:00 [INF] Hosting environment: Development
2025-06-24 16:19:29.911 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-24 16:19:34.766 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-24 16:19:34.886 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 121.0269ms
2025-06-24 16:19:35.257 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 16:19:35.438 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 181.0471ms
2025-06-24 16:22:29.308 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/login - application/json 52
2025-06-24 16:22:29.333 +03:00 [INF] CORS policy execution successful.
2025-06-24 16:22:29.365 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 16:22:29.385 +03:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(GamalComapany.Service.Dtos.UserDto.LoginDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 16:22:31.095 +03:00 [INF] Executed DbCommand (52ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 16:22:31.239 +03:00 [INF] Access token generated for user 2
2025-06-24 16:22:31.412 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-06-24 16:22:31.438 +03:00 [INF] User logged in successfully: Admin
2025-06-24 16:22:31.447 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.LoginResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 16:22:31.485 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API) in 2093.5861ms
2025-06-24 16:22:31.488 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 16:22:31.494 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/login - 200 null application/json; charset=utf-8 2185.6486ms
2025-06-24 16:25:01.823 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/Partner - application/json 154
2025-06-24 16:25:01.828 +03:00 [INF] CORS policy execution successful.
2025-06-24 16:25:01.880 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CreatePartner (GamalComapnyApp.API)'
2025-06-24 16:25:01.887 +03:00 [INF] Route matched with {action = "CreatePartner", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartner(GamalComapany.Service.Dtos.PartnerDto.CreatePartnerDto) on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-24 16:25:02.020 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-06-24 16:25:02.080 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (Size = 4000), @p7='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p8='?' (DbType = DateTime2), @p9='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Partners] ([CreatedAt], [CreatedBy], [Description], [InitialInvestment], [IsActive], [IsDeleted], [NameEn], [SharePercentage], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9);
2025-06-24 16:25:02.118 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [p1].[Id], [p1].[CreatedAt], [p1].[CreatedBy], [p1].[Description], [p1].[InitialInvestment], [p1].[IsActive], [p1].[IsDeleted], [p1].[NameEn], [p1].[SharePercentage], [p1].[UpdatedAt], [p1].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM (
    SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [Partners] AS [p]
    WHERE [p].[Id] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)
) AS [p1]
LEFT JOIN [PartnerTransations] AS [p0] ON [p1].[Id] = [p0].[PartnerId]
ORDER BY [p1].[Id]
2025-06-24 16:25:02.134 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.PartnerDto.PartnerResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 16:25:02.148 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.CreatePartner (GamalComapnyApp.API) in 257.9102ms
2025-06-24 16:25:02.151 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CreatePartner (GamalComapnyApp.API)'
2025-06-24 16:25:02.153 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/Partner - 200 null application/json; charset=utf-8 329.2914ms
2025-06-24 16:27:01.034 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner/capital-calculation - null null
2025-06-24 16:27:01.042 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CalculatePartnersCapital (GamalComapnyApp.API)'
2025-06-24 16:27:01.047 +03:00 [INF] Route matched with {action = "CalculatePartnersCapital", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CalculatePartnersCapital() on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-24 16:27:01.108 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-06-24 16:27:01.114 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.PartnerDto.PartnerCapitalCalculationDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 16:27:01.119 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.CalculatePartnersCapital (GamalComapnyApp.API) in 70.3925ms
2025-06-24 16:27:01.121 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CalculatePartnersCapital (GamalComapnyApp.API)'
2025-06-24 16:27:01.123 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner/capital-calculation - 200 null application/json; charset=utf-8 88.7976ms
2025-06-24 16:28:37.167 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/Partner - application/json 137
2025-06-24 16:28:37.170 +03:00 [INF] CORS policy execution successful.
2025-06-24 16:28:37.174 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CreatePartner (GamalComapnyApp.API)'
2025-06-24 16:28:37.175 +03:00 [INF] Route matched with {action = "CreatePartner", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartner(GamalComapany.Service.Dtos.PartnerDto.CreatePartnerDto) on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-24 16:28:37.221 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-06-24 16:28:37.224 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.PartnerDto.PartnerResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 16:28:37.226 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.CreatePartner (GamalComapnyApp.API) in 48.6524ms
2025-06-24 16:28:37.229 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CreatePartner (GamalComapnyApp.API)'
2025-06-24 16:28:37.231 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/Partner - 400 null application/json; charset=utf-8 63.8051ms
2025-06-24 16:31:18.254 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/Partner - application/json 142
2025-06-24 16:31:18.257 +03:00 [INF] CORS policy execution successful.
2025-06-24 16:31:18.259 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CreatePartner (GamalComapnyApp.API)'
2025-06-24 16:31:18.260 +03:00 [INF] Route matched with {action = "CreatePartner", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartner(GamalComapany.Service.Dtos.PartnerDto.CreatePartnerDto) on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-24 16:31:18.280 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-06-24 16:31:18.289 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (Size = 4000), @p7='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p8='?' (DbType = DateTime2), @p9='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Partners] ([CreatedAt], [CreatedBy], [Description], [InitialInvestment], [IsActive], [IsDeleted], [NameEn], [SharePercentage], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9);
2025-06-24 16:31:18.303 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [p1].[Id], [p1].[CreatedAt], [p1].[CreatedBy], [p1].[Description], [p1].[InitialInvestment], [p1].[IsActive], [p1].[IsDeleted], [p1].[NameEn], [p1].[SharePercentage], [p1].[UpdatedAt], [p1].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM (
    SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [Partners] AS [p]
    WHERE [p].[Id] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)
) AS [p1]
LEFT JOIN [PartnerTransations] AS [p0] ON [p1].[Id] = [p0].[PartnerId]
ORDER BY [p1].[Id]
2025-06-24 16:31:18.306 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.PartnerDto.PartnerResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 16:31:18.309 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.CreatePartner (GamalComapnyApp.API) in 47.0718ms
2025-06-24 16:31:18.310 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CreatePartner (GamalComapnyApp.API)'
2025-06-24 16:31:18.311 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/Partner - 200 null application/json; charset=utf-8 57.0876ms
2025-06-24 16:33:08.167 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner/capital-calculation - null null
2025-06-24 16:33:08.172 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CalculatePartnersCapital (GamalComapnyApp.API)'
2025-06-24 16:33:08.173 +03:00 [INF] Route matched with {action = "CalculatePartnersCapital", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CalculatePartnersCapital() on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-24 16:33:08.199 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-06-24 16:33:08.202 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.PartnerDto.PartnerCapitalCalculationDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 16:33:08.204 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.CalculatePartnersCapital (GamalComapnyApp.API) in 21.0971ms
2025-06-24 16:33:08.206 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CalculatePartnersCapital (GamalComapnyApp.API)'
2025-06-24 16:33:08.207 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner/capital-calculation - 200 null application/json; charset=utf-8 40.2294ms
2025-06-24 16:33:37.749 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner - null null
2025-06-24 16:33:37.752 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.GetAllPartners (GamalComapnyApp.API)'
2025-06-24 16:33:37.756 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners() on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-24 16:33:37.776 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-06-24 16:33:37.780 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.PartnerDto.PartnerResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 16:33:37.783 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.GetAllPartners (GamalComapnyApp.API) in 23.6954ms
2025-06-24 16:33:37.784 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.GetAllPartners (GamalComapnyApp.API)'
2025-06-24 16:33:37.786 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner - 200 null application/json; charset=utf-8 37.3715ms
2025-06-24 16:34:22.560 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/Partner/recalculate-shares - null 0
2025-06-24 16:34:22.564 +03:00 [INF] CORS policy execution successful.
2025-06-24 16:34:22.565 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.RecalculateSharePercentages (GamalComapnyApp.API)'
2025-06-24 16:34:22.584 +03:00 [INF] Route matched with {action = "RecalculateSharePercentages", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] RecalculateSharePercentages() on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-24 16:34:22.603 +03:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-06-24 16:34:22.633 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Id] = @__p_0
2025-06-24 16:34:22.645 +03:00 [WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'.
2025-06-24 16:34:22.651 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@p8='?' (DbType = Int32), @p0='?' (Size = 4000), @p1='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p2='?' (DbType = Boolean), @p3='?' (DbType = Boolean), @p4='?' (Size = 4000), @p5='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p6='?' (DbType = DateTime2), @p7='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Partners] SET [Description] = @p0, [InitialInvestment] = @p1, [IsActive] = @p2, [IsDeleted] = @p3, [NameEn] = @p4, [SharePercentage] = @p5, [UpdatedAt] = @p6, [UpdatedBy] = @p7
OUTPUT 1
WHERE [Id] = @p8;
2025-06-24 16:34:22.657 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Id] = @__p_0
2025-06-24 16:34:22.682 +03:00 [WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'.
2025-06-24 16:34:22.685 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@p8='?' (DbType = Int32), @p0='?' (Size = 4000), @p1='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p2='?' (DbType = Boolean), @p3='?' (DbType = Boolean), @p4='?' (Size = 4000), @p5='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p6='?' (DbType = DateTime2), @p7='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Partners] SET [Description] = @p0, [InitialInvestment] = @p1, [IsActive] = @p2, [IsDeleted] = @p3, [NameEn] = @p4, [SharePercentage] = @p5, [UpdatedAt] = @p6, [UpdatedBy] = @p7
OUTPUT 1
WHERE [Id] = @p8;
2025-06-24 16:34:22.696 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 16:34:22.699 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.RecalculateSharePercentages (GamalComapnyApp.API) in 110.6025ms
2025-06-24 16:34:22.701 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.RecalculateSharePercentages (GamalComapnyApp.API)'
2025-06-24 16:34:22.702 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/Partner/recalculate-shares - 200 null application/json; charset=utf-8 141.3007ms
2025-06-24 16:34:48.862 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner/1/transactions - null null
2025-06-24 16:34:48.867 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.GetPartnerTransactions (GamalComapnyApp.API)'
2025-06-24 16:34:48.872 +03:00 [INF] Route matched with {action = "GetPartnerTransactions", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetPartnerTransactions(Int32) on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-24 16:34:48.903 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[@__partnerId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[PartnerId] = @__partnerId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[TransactionDate] DESC
2025-06-24 16:34:48.909 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.PartnerDto.PartnerTransactionResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 16:34:48.913 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.GetPartnerTransactions (GamalComapnyApp.API) in 34.8731ms
2025-06-24 16:34:48.915 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.GetPartnerTransactions (GamalComapnyApp.API)'
2025-06-24 16:34:48.916 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner/1/transactions - 200 null application/json; charset=utf-8 53.5937ms
2025-06-24 17:08:45.960 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/Partner/recalculate-shares - null 0
2025-06-24 17:08:45.963 +03:00 [INF] CORS policy execution successful.
2025-06-24 17:08:45.965 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.RecalculateSharePercentages (GamalComapnyApp.API)'
2025-06-24 17:08:45.966 +03:00 [INF] Route matched with {action = "RecalculateSharePercentages", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] RecalculateSharePercentages() on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-24 17:08:46.001 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-06-24 17:08:46.009 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Id] = @__p_0
2025-06-24 17:08:46.012 +03:00 [WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'.
2025-06-24 17:08:46.016 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@p8='?' (DbType = Int32), @p0='?' (Size = 4000), @p1='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p2='?' (DbType = Boolean), @p3='?' (DbType = Boolean), @p4='?' (Size = 4000), @p5='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p6='?' (DbType = DateTime2), @p7='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Partners] SET [Description] = @p0, [InitialInvestment] = @p1, [IsActive] = @p2, [IsDeleted] = @p3, [NameEn] = @p4, [SharePercentage] = @p5, [UpdatedAt] = @p6, [UpdatedBy] = @p7
OUTPUT 1
WHERE [Id] = @p8;
2025-06-24 17:08:46.020 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Id] = @__p_0
2025-06-24 17:08:46.022 +03:00 [WRN] Savepoints are disabled because Multiple Active Result Sets (MARS) is enabled. If 'SaveChanges' fails, then the transaction cannot be automatically rolled back to a known clean state. Instead, the transaction should be rolled back by the application before retrying 'SaveChanges'. See https://go.microsoft.com/fwlink/?linkid=2149338 for more information and examples. To identify the code which triggers this warning, call 'ConfigureWarnings(w => w.Throw(SqlServerEventId.SavepointsDisabledBecauseOfMARS))'.
2025-06-24 17:08:46.024 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@p8='?' (DbType = Int32), @p0='?' (Size = 4000), @p1='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p2='?' (DbType = Boolean), @p3='?' (DbType = Boolean), @p4='?' (Size = 4000), @p5='?' (Precision = 18) (Scale = 2) (DbType = Decimal), @p6='?' (DbType = DateTime2), @p7='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Partners] SET [Description] = @p0, [InitialInvestment] = @p1, [IsActive] = @p2, [IsDeleted] = @p3, [NameEn] = @p4, [SharePercentage] = @p5, [UpdatedAt] = @p6, [UpdatedBy] = @p7
OUTPUT 1
WHERE [Id] = @p8;
2025-06-24 17:08:46.027 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 17:08:46.028 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.RecalculateSharePercentages (GamalComapnyApp.API) in 60.3939ms
2025-06-24 17:08:46.030 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.RecalculateSharePercentages (GamalComapnyApp.API)'
2025-06-24 17:08:46.031 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/Partner/recalculate-shares - 200 null application/json; charset=utf-8 70.9223ms
2025-06-24 17:09:00.705 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner/capital-calculation - null null
2025-06-24 17:09:00.709 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CalculatePartnersCapital (GamalComapnyApp.API)'
2025-06-24 17:09:00.710 +03:00 [INF] Route matched with {action = "CalculatePartnersCapital", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CalculatePartnersCapital() on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-24 17:09:00.714 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-06-24 17:09:00.733 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.PartnerDto.PartnerCapitalCalculationDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 17:09:00.735 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.CalculatePartnersCapital (GamalComapnyApp.API) in 23.8698ms
2025-06-24 17:09:00.738 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CalculatePartnersCapital (GamalComapnyApp.API)'
2025-06-24 17:09:00.739 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner/capital-calculation - 200 null application/json; charset=utf-8 34.2997ms
2025-06-24 17:09:13.599 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner - null null
2025-06-24 17:09:13.603 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.GetAllPartners (GamalComapnyApp.API)'
2025-06-24 17:09:13.604 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners() on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-24 17:09:13.608 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-06-24 17:09:13.611 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.PartnerDto.PartnerResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 17:09:13.613 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.GetAllPartners (GamalComapnyApp.API) in 7.3671ms
2025-06-24 17:09:13.616 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.GetAllPartners (GamalComapnyApp.API)'
2025-06-24 17:09:13.617 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner - 200 null application/json; charset=utf-8 17.89ms
2025-06-24 17:27:02.613 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/Partner/transactions - application/json 187
2025-06-24 17:27:02.616 +03:00 [INF] CORS policy execution successful.
2025-06-24 17:27:02.617 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CreatePartnerTransaction (GamalComapnyApp.API)'
2025-06-24 17:27:02.642 +03:00 [INF] Route matched with {action = "CreatePartnerTransaction", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreatePartnerTransaction(GamalComapany.Service.Dtos.PartnerDto.PartnerTransactionDto) on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-24 17:27:02.696 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[Id] = @__p_0
2025-06-24 17:27:02.747 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (Precision = 18) (Scale = 4) (DbType = Decimal), @p2='?' (DbType = DateTime2), @p3='?' (DbType = Int32), @p4='?' (Size = 4000), @p5='?' (Size = 4000), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (Size = 4000), @p9='?' (DbType = Int32), @p10='?' (DbType = DateTime2), @p11='?' (DbType = DateTime2), @p12='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [PartnerTransations] ([ActionDetailId], [Amount], [CreatedAt], [CreatedBy], [Description], [ImagePath], [IsActive], [IsDeleted], [Notes], [PartnerId], [TransactionDate], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12);
2025-06-24 17:27:02.755 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.PartnerDto.PartnerTransactionResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 17:27:02.760 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.CreatePartnerTransaction (GamalComapnyApp.API) in 115.0893ms
2025-06-24 17:27:02.762 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CreatePartnerTransaction (GamalComapnyApp.API)'
2025-06-24 17:27:02.763 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/Partner/transactions - 200 null application/json; charset=utf-8 150.3844ms
2025-06-24 17:30:50.468 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner/1/transactions - null null
2025-06-24 17:30:50.478 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 02:22:31 م', Current time (UTC): '24/06/2025 02:30:50 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-24 17:30:50.492 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 02:22:31 م', Current time (UTC): '24/06/2025 02:30:50 م'.
2025-06-24 17:30:50.495 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 02:22:31 م', Current time (UTC): '24/06/2025 02:30:50 م'.
2025-06-24 17:30:50.497 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 17:30:50.498 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 17:30:50.504 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 17:30:50.505 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner/1/transactions - 401 0 null 36.8309ms
2025-06-24 17:30:55.434 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner/1/transactions - null null
2025-06-24 17:30:55.437 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 02:22:31 م', Current time (UTC): '24/06/2025 02:30:55 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-24 17:30:55.440 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 02:22:31 م', Current time (UTC): '24/06/2025 02:30:55 م'.
2025-06-24 17:30:55.441 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 02:22:31 م', Current time (UTC): '24/06/2025 02:30:55 م'.
2025-06-24 17:30:55.442 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 17:30:55.443 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 17:30:55.466 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 17:30:55.468 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner/1/transactions - 401 0 null 34.3061ms
2025-06-24 17:30:57.935 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner/1/transactions - null null
2025-06-24 17:30:57.938 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 02:22:31 م', Current time (UTC): '24/06/2025 02:30:57 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-24 17:30:57.941 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 02:22:31 م', Current time (UTC): '24/06/2025 02:30:57 م'.
2025-06-24 17:30:57.942 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 02:22:31 م', Current time (UTC): '24/06/2025 02:30:57 م'.
2025-06-24 17:30:57.966 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 17:30:57.967 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 17:30:57.969 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 17:30:57.970 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner/1/transactions - 401 0 null 34.4118ms
2025-06-24 17:30:58.258 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner/1/transactions - null null
2025-06-24 17:30:58.262 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 02:22:31 م', Current time (UTC): '24/06/2025 02:30:58 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-24 17:30:58.264 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 02:22:31 م', Current time (UTC): '24/06/2025 02:30:58 م'.
2025-06-24 17:30:58.265 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 02:22:31 م', Current time (UTC): '24/06/2025 02:30:58 م'.
2025-06-24 17:30:58.266 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 17:30:58.267 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 17:30:58.269 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 17:30:58.270 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner/1/transactions - 401 0 null 12.1863ms
2025-06-24 17:30:58.562 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner/1/transactions - null null
2025-06-24 17:30:58.566 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 02:22:31 م', Current time (UTC): '24/06/2025 02:30:58 م'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-24 17:30:58.568 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 02:22:31 م', Current time (UTC): '24/06/2025 02:30:58 م'.
2025-06-24 17:30:58.568 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 02:22:31 م', Current time (UTC): '24/06/2025 02:30:58 م'.
2025-06-24 17:30:58.585 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 17:30:58.593 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 17:30:58.598 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 17:30:58.599 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner/1/transactions - 401 0 null 36.851ms
2025-06-24 17:31:08.982 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 17:31:09.017 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 35.1786ms
2025-06-24 17:31:21.545 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner - null null
2025-06-24 17:31:21.549 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 17:31:21.550 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 17:31:21.551 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 17:31:21.552 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner - 401 0 null 6.5327ms
2025-06-24 17:31:24.881 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner - null null
2025-06-24 17:31:24.885 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 17:31:24.886 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 17:31:24.887 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 17:31:24.889 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner - 401 0 null 7.8649ms
2025-06-24 17:32:01.512 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/login - application/json 52
2025-06-24 17:32:01.517 +03:00 [INF] CORS policy execution successful.
2025-06-24 17:32:01.518 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 17:32:01.519 +03:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(GamalComapany.Service.Dtos.UserDto.LoginDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 17:32:01.551 +03:00 [INF] Executed DbCommand (26ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 17:32:01.643 +03:00 [INF] Access token generated for user 2
2025-06-24 17:32:01.651 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-06-24 17:32:01.655 +03:00 [INF] User logged in successfully: Admin
2025-06-24 17:32:01.656 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.LoginResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 17:32:01.657 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API) in 135.9178ms
2025-06-24 17:32:01.659 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 17:32:01.660 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/login - 200 null application/json; charset=utf-8 148.3925ms
2025-06-24 17:33:11.768 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner/1/transactions - null null
2025-06-24 17:33:11.772 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.GetPartnerTransactions (GamalComapnyApp.API)'
2025-06-24 17:33:11.773 +03:00 [INF] Route matched with {action = "GetPartnerTransactions", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetPartnerTransactions(Int32) on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-24 17:33:11.792 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[@__partnerId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[PartnerId] = @__partnerId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[TransactionDate] DESC
2025-06-24 17:33:11.815 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.PartnerDto.PartnerTransactionResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 17:33:11.821 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.GetPartnerTransactions (GamalComapnyApp.API) in 45.5151ms
2025-06-24 17:33:11.822 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.GetPartnerTransactions (GamalComapnyApp.API)'
2025-06-24 17:33:11.824 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner/1/transactions - 200 null application/json; charset=utf-8 55.7139ms
2025-06-24 17:33:48.293 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner/capital-calculation - null null
2025-06-24 17:33:48.297 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CalculatePartnersCapital (GamalComapnyApp.API)'
2025-06-24 17:33:48.298 +03:00 [INF] Route matched with {action = "CalculatePartnersCapital", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CalculatePartnersCapital() on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-24 17:33:48.318 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-06-24 17:33:48.322 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.PartnerDto.PartnerCapitalCalculationDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 17:33:48.324 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.CalculatePartnersCapital (GamalComapnyApp.API) in 23.8575ms
2025-06-24 17:33:48.325 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CalculatePartnersCapital (GamalComapnyApp.API)'
2025-06-24 17:33:48.326 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner/capital-calculation - 200 null application/json; charset=utf-8 33.2128ms
2025-06-24 17:34:16.284 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner/1 - null null
2025-06-24 17:34:16.287 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.GetPartnerById (GamalComapnyApp.API)'
2025-06-24 17:34:16.292 +03:00 [INF] Route matched with {action = "GetPartnerById", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetPartnerById(Int32) on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-24 17:34:16.322 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [p1].[Id], [p1].[CreatedAt], [p1].[CreatedBy], [p1].[Description], [p1].[InitialInvestment], [p1].[IsActive], [p1].[IsDeleted], [p1].[NameEn], [p1].[SharePercentage], [p1].[UpdatedAt], [p1].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM (
    SELECT TOP(1) [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy]
    FROM [Partners] AS [p]
    WHERE [p].[Id] = @__id_0 AND [p].[IsDeleted] = CAST(0 AS bit)
) AS [p1]
LEFT JOIN [PartnerTransations] AS [p0] ON [p1].[Id] = [p0].[PartnerId]
ORDER BY [p1].[Id]
2025-06-24 17:34:16.329 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.PartnerDto.PartnerResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 17:34:16.332 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.GetPartnerById (GamalComapnyApp.API) in 18.873ms
2025-06-24 17:34:16.334 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.GetPartnerById (GamalComapnyApp.API)'
2025-06-24 17:34:16.336 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner/1 - 200 null application/json; charset=utf-8 52.3451ms
2025-06-24 17:35:41.997 +03:00 [INF] Application is shutting down...
2025-06-24 18:26:38.507 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-24 18:26:38.795 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-24 18:26:38.871 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 18:26:38.873 +03:00 [INF] Hosting environment: Development
2025-06-24 18:26:38.874 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-24 18:27:44.029 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-24 18:27:44.282 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 257.7805ms
2025-06-24 18:28:08.810 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Financial/treasuries - null null
2025-06-24 18:28:08.889 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-24 18:28:08.891 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-24 18:28:08.896 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-24 18:28:08.900 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Financial/treasuries - 401 0 null 89.3818ms
2025-06-24 18:32:07.859 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/login - application/json 51
2025-06-24 18:32:07.870 +03:00 [INF] CORS policy execution successful.
2025-06-24 18:32:07.875 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 18:32:07.901 +03:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(GamalComapany.Service.Dtos.UserDto.LoginDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 18:32:09.938 +03:00 [INF] Executed DbCommand (54ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 18:32:10.094 +03:00 [WRN] Login attempt with invalid password for user: Admin
2025-06-24 18:32:10.106 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.LoginResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 18:32:10.135 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API) in 2228.1224ms
2025-06-24 18:32:10.138 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 18:32:10.146 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/login - 401 null application/json; charset=utf-8 2286.9803ms
2025-06-24 18:32:26.082 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/login - application/json 52
2025-06-24 18:32:26.089 +03:00 [INF] CORS policy execution successful.
2025-06-24 18:32:26.092 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 18:32:26.111 +03:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(GamalComapany.Service.Dtos.UserDto.LoginDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-24 18:32:26.177 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-24 18:32:26.323 +03:00 [INF] Access token generated for user 2
2025-06-24 18:32:26.521 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-06-24 18:32:26.549 +03:00 [INF] User logged in successfully: Admin
2025-06-24 18:32:26.551 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.LoginResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 18:32:26.562 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API) in 443.5103ms
2025-06-24 18:32:26.564 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-24 18:32:26.566 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/login - 200 null application/json; charset=utf-8 483.7727ms
2025-06-24 18:33:04.274 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Financial/treasuries - null null
2025-06-24 18:33:04.349 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetAllTreasuries (GamalComapnyApp.API)'
2025-06-24 18:33:04.355 +03:00 [INF] Route matched with {action = "GetAllTreasuries", controller = "Financial"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllTreasuries() on controller GamalComapnyApp.API.Controllers.FinancialController (GamalComapnyApp.API).
2025-06-24 18:33:04.399 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[CreatedAt], [t].[CreatedBy], [t].[Description], [t].[IsActive], [t].[IsDeleted], [t].[NameEn], [t].[UpdatedAt], [t].[UpdatedBy]
FROM [Treasuries] AS [t]
WHERE [t].[IsDeleted] = CAST(0 AS bit) AND [t].[IsActive] = CAST(1 AS bit)
2025-06-24 18:33:04.413 +03:00 [INF] Retrieved 0 treasuries
2025-06-24 18:33:04.415 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.FinancialDto.TreasuryResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 18:33:04.426 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.FinancialController.GetAllTreasuries (GamalComapnyApp.API) in 68.0698ms
2025-06-24 18:33:04.430 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetAllTreasuries (GamalComapnyApp.API)'
2025-06-24 18:33:04.432 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Financial/treasuries - 200 null application/json; charset=utf-8 157.7153ms
2025-06-24 18:34:20.248 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/Financial/treasuries - application/json 105
2025-06-24 18:34:20.252 +03:00 [INF] CORS policy execution successful.
2025-06-24 18:34:20.255 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.FinancialController.CreateTreasury (GamalComapnyApp.API)'
2025-06-24 18:34:20.260 +03:00 [INF] Route matched with {action = "CreateTreasury", controller = "Financial"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateTreasury(GamalComapany.Service.Dtos.FinancialDto.CreateTreasuryDto) on controller GamalComapnyApp.API.Controllers.FinancialController (GamalComapnyApp.API).
2025-06-24 18:34:20.299 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__treasuryDto_NameEn_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[CreatedAt], [t].[CreatedBy], [t].[Description], [t].[IsActive], [t].[IsDeleted], [t].[NameEn], [t].[UpdatedAt], [t].[UpdatedBy]
FROM [Treasuries] AS [t]
WHERE [t].[NameEn] = @__treasuryDto_NameEn_0 AND [t].[IsDeleted] = CAST(0 AS bit)
2025-06-24 18:34:20.334 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (Size = 4000), @p6='?' (DbType = DateTime2), @p7='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Treasuries] ([CreatedAt], [CreatedBy], [Description], [IsActive], [IsDeleted], [NameEn], [UpdatedAt], [UpdatedBy])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7);
2025-06-24 18:34:20.341 +03:00 [INF] Treasury created successfully: 1 - الخزينة الرئيسة
2025-06-24 18:34:20.343 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.FinancialDto.TreasuryResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 18:34:20.351 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.FinancialController.CreateTreasury (GamalComapnyApp.API) in 87.5557ms
2025-06-24 18:34:20.355 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.FinancialController.CreateTreasury (GamalComapnyApp.API)'
2025-06-24 18:34:20.356 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/Financial/treasuries - 200 null application/json; charset=utf-8 109.8657ms
2025-06-24 18:34:33.758 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Financial/treasuries - null null
2025-06-24 18:34:33.762 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetAllTreasuries (GamalComapnyApp.API)'
2025-06-24 18:34:33.764 +03:00 [INF] Route matched with {action = "GetAllTreasuries", controller = "Financial"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllTreasuries() on controller GamalComapnyApp.API.Controllers.FinancialController (GamalComapnyApp.API).
2025-06-24 18:34:33.799 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[CreatedAt], [t].[CreatedBy], [t].[Description], [t].[IsActive], [t].[IsDeleted], [t].[NameEn], [t].[UpdatedAt], [t].[UpdatedBy]
FROM [Treasuries] AS [t]
WHERE [t].[IsDeleted] = CAST(0 AS bit) AND [t].[IsActive] = CAST(1 AS bit)
2025-06-24 18:34:33.803 +03:00 [INF] Retrieved 1 treasuries
2025-06-24 18:34:33.805 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.FinancialDto.TreasuryResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 18:34:33.807 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.FinancialController.GetAllTreasuries (GamalComapnyApp.API) in 41.6059ms
2025-06-24 18:34:33.810 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetAllTreasuries (GamalComapnyApp.API)'
2025-06-24 18:34:33.811 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Financial/treasuries - 200 null application/json; charset=utf-8 53.0097ms
2025-06-24 18:38:57.495 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Financial/treasuries/1/balance?fromDate=1-1-2025&toDate=1-1-2025 - null null
2025-06-24 18:38:57.500 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetTreasuryBalance (GamalComapnyApp.API)'
2025-06-24 18:38:57.506 +03:00 [INF] Route matched with {action = "GetTreasuryBalance", controller = "Financial"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTreasuryBalance(Int32, System.DateTime, System.DateTime) on controller GamalComapnyApp.API.Controllers.FinancialController (GamalComapnyApp.API).
2025-06-24 18:38:57.550 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[CreatedAt], [t].[CreatedBy], [t].[Description], [t].[IsActive], [t].[IsDeleted], [t].[NameEn], [t].[UpdatedAt], [t].[UpdatedBy]
FROM [Treasuries] AS [t]
WHERE [t].[Id] = @__p_0
2025-06-24 18:38:57.585 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[@__treasuryId_0='?' (DbType = Int32), @__fromDate_1='?' (DbType = DateTime2), @__toDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[ActionTypeId], [t].[Amount], [t].[CreatedAt], [t].[CreatedBy], [t].[Description], [t].[FinancialTransactionId], [t].[ImagePath], [t].[IsActive], [t].[IsDeleted], [t].[Notes], [t].[ReferenceNumber], [t].[TransactionDate], [t].[TreasuryId], [t].[UpdatedAt], [t].[UpdatedBy], [m].[Id], [m].[ActionTypeId], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[IsActive], [m].[IsDeleted], [m].[NameEn], [m].[ParentActionId], [m].[UpdatedAt], [m].[UpdatedBy], [a].[Id], [a].[CreatedAt], [a].[CreatedBy], [a].[Description], [a].[IsActive], [a].[IsDeleted], [a].[NameEn], [a].[UpdatedAt], [a].[UpdatedBy]
FROM [TreasuryTransactions] AS [t]
INNER JOIN [MainActions] AS [m] ON [t].[ActionTypeId] = [m].[Id]
LEFT JOIN [ActionTypes] AS [a] ON [m].[ActionTypeId] = [a].[Id]
WHERE [t].[TreasuryId] = @__treasuryId_0 AND [t].[IsDeleted] = CAST(0 AS bit) AND [t].[TransactionDate] >= @__fromDate_1 AND [t].[TransactionDate] <= @__toDate_2
2025-06-24 18:38:57.606 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__treasuryId_0='?' (DbType = Int32), @__date_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[ActionTypeId], [t].[Amount], [t].[CreatedAt], [t].[CreatedBy], [t].[Description], [t].[FinancialTransactionId], [t].[ImagePath], [t].[IsActive], [t].[IsDeleted], [t].[Notes], [t].[ReferenceNumber], [t].[TransactionDate], [t].[TreasuryId], [t].[UpdatedAt], [t].[UpdatedBy], [m].[Id], [m].[ActionTypeId], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[IsActive], [m].[IsDeleted], [m].[NameEn], [m].[ParentActionId], [m].[UpdatedAt], [m].[UpdatedBy], [a].[Id], [a].[CreatedAt], [a].[CreatedBy], [a].[Description], [a].[IsActive], [a].[IsDeleted], [a].[NameEn], [a].[UpdatedAt], [a].[UpdatedBy]
FROM [TreasuryTransactions] AS [t]
INNER JOIN [MainActions] AS [m] ON [t].[ActionTypeId] = [m].[Id]
LEFT JOIN [ActionTypes] AS [a] ON [m].[ActionTypeId] = [a].[Id]
WHERE [t].[TreasuryId] = @__treasuryId_0 AND [t].[IsDeleted] = CAST(0 AS bit) AND [t].[TransactionDate] < @__date_1
2025-06-24 18:38:57.617 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.FinancialDto.TreasuryBalanceDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 18:38:57.623 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.FinancialController.GetTreasuryBalance (GamalComapnyApp.API) in 114.0876ms
2025-06-24 18:38:57.629 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetTreasuryBalance (GamalComapnyApp.API)'
2025-06-24 18:38:57.630 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Financial/treasuries/1/balance?fromDate=1-1-2025&toDate=1-1-2025 - 200 null application/json; charset=utf-8 139.4051ms
2025-06-24 18:39:24.349 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Financial/treasuries/balance - null null
2025-06-24 18:39:24.356 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetAllTreasuriesBalance (GamalComapnyApp.API)'
2025-06-24 18:39:24.385 +03:00 [INF] Route matched with {action = "GetAllTreasuriesBalance", controller = "Financial"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllTreasuriesBalance(System.DateTime, System.DateTime) on controller GamalComapnyApp.API.Controllers.FinancialController (GamalComapnyApp.API).
2025-06-24 18:39:24.394 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[CreatedAt], [t].[CreatedBy], [t].[Description], [t].[IsActive], [t].[IsDeleted], [t].[NameEn], [t].[UpdatedAt], [t].[UpdatedBy]
FROM [Treasuries] AS [t]
WHERE [t].[IsDeleted] = CAST(0 AS bit) AND [t].[IsActive] = CAST(1 AS bit)
2025-06-24 18:39:24.398 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[CreatedAt], [t].[CreatedBy], [t].[Description], [t].[IsActive], [t].[IsDeleted], [t].[NameEn], [t].[UpdatedAt], [t].[UpdatedBy]
FROM [Treasuries] AS [t]
WHERE [t].[Id] = @__p_0
2025-06-24 18:39:24.401 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__treasuryId_0='?' (DbType = Int32), @__fromDate_1='?' (DbType = DateTime2), @__toDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[ActionTypeId], [t].[Amount], [t].[CreatedAt], [t].[CreatedBy], [t].[Description], [t].[FinancialTransactionId], [t].[ImagePath], [t].[IsActive], [t].[IsDeleted], [t].[Notes], [t].[ReferenceNumber], [t].[TransactionDate], [t].[TreasuryId], [t].[UpdatedAt], [t].[UpdatedBy], [m].[Id], [m].[ActionTypeId], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[IsActive], [m].[IsDeleted], [m].[NameEn], [m].[ParentActionId], [m].[UpdatedAt], [m].[UpdatedBy], [a].[Id], [a].[CreatedAt], [a].[CreatedBy], [a].[Description], [a].[IsActive], [a].[IsDeleted], [a].[NameEn], [a].[UpdatedAt], [a].[UpdatedBy]
FROM [TreasuryTransactions] AS [t]
INNER JOIN [MainActions] AS [m] ON [t].[ActionTypeId] = [m].[Id]
LEFT JOIN [ActionTypes] AS [a] ON [m].[ActionTypeId] = [a].[Id]
WHERE [t].[TreasuryId] = @__treasuryId_0 AND [t].[IsDeleted] = CAST(0 AS bit) AND [t].[TransactionDate] >= @__fromDate_1 AND [t].[TransactionDate] <= @__toDate_2
2025-06-24 18:39:24.404 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__treasuryId_0='?' (DbType = Int32), @__date_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[ActionTypeId], [t].[Amount], [t].[CreatedAt], [t].[CreatedBy], [t].[Description], [t].[FinancialTransactionId], [t].[ImagePath], [t].[IsActive], [t].[IsDeleted], [t].[Notes], [t].[ReferenceNumber], [t].[TransactionDate], [t].[TreasuryId], [t].[UpdatedAt], [t].[UpdatedBy], [m].[Id], [m].[ActionTypeId], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[IsActive], [m].[IsDeleted], [m].[NameEn], [m].[ParentActionId], [m].[UpdatedAt], [m].[UpdatedBy], [a].[Id], [a].[CreatedAt], [a].[CreatedBy], [a].[Description], [a].[IsActive], [a].[IsDeleted], [a].[NameEn], [a].[UpdatedAt], [a].[UpdatedBy]
FROM [TreasuryTransactions] AS [t]
INNER JOIN [MainActions] AS [m] ON [t].[ActionTypeId] = [m].[Id]
LEFT JOIN [ActionTypes] AS [a] ON [m].[ActionTypeId] = [a].[Id]
WHERE [t].[TreasuryId] = @__treasuryId_0 AND [t].[IsDeleted] = CAST(0 AS bit) AND [t].[TransactionDate] < @__date_1
2025-06-24 18:39:24.411 +03:00 [INF] Retrieved balance for 1 treasuries
2025-06-24 18:39:24.412 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.FinancialDto.TreasuryBalanceDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 18:39:24.416 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.FinancialController.GetAllTreasuriesBalance (GamalComapnyApp.API) in 28.3247ms
2025-06-24 18:39:24.417 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetAllTreasuriesBalance (GamalComapnyApp.API)'
2025-06-24 18:39:24.419 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Financial/treasuries/balance - 200 null application/json; charset=utf-8 73.2387ms
