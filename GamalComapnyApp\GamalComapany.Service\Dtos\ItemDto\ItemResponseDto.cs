using GamalComapany.Service.Dtos.InventoryDto;

namespace GamalComapany.Service.Dtos.ItemDto
{
    public class ItemResponseDto
    {
        public int Id { get; set; }
        public string NameEn { get; set; } = string.Empty;
        public string? NameAr { get; set; }
        public string? Description { get; set; }
        public string? ItemCode { get; set; }
        public int CategoryId { get; set; }
        public string? CategoryName { get; set; }
        public string? Barcode { get; set; }
        public int UnitId { get; set; }
        public string? UnitName { get; set; }
        public decimal StandardCost { get; set; }
        public int? MinimumStock { get; set; }
        public int? MaximumStock { get; set; }
        public int? ReorderLevel { get; set; }
        public int? SortOrder { get; set; }
        public string? ItemType { get; set; }
        public string? ItemTypeName { get; set; }
        public decimal CurrentStock { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public List<ItemImageResponseDto> Images { get; set; } = new List<ItemImageResponseDto>();
    }
}
