using AutoMapper;
using GamalComapany.Service.Dtos.CompanyDto;
using GamalComapany.Service.Dtos.PartnerDto;
using GamalComapany.Service.Dtos.InventoryDto;
using GamalComapany.Service.Dtos.FinancialDto;
using GamalComapany.Service.Dtos.SupplierCustomerDto;
using GamalComapany.Service.Dtos.UserDto;
using GamalComapany.Service.Dtos.ItemDto;
using GamalCompany.Data.Models;

namespace GamalComapany.Service.Mapping
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // Company Mappings
            CreateMap<Company, CreateCompanyDto>().ReverseMap();
            CreateMap<Company, UpdateCompanyDto>().ReverseMap();
            CreateMap<CompanyDepartment, CearteDepartmentDto>().ReverseMap();

            // Partner Mappings
            CreateMap<Partner, CreatePartnerDto>().ReverseMap();
            CreateMap<Partner, UpdatePartnerDto>().ReverseMap();
            CreateMap<Partner, PartnerResponseDto>()
                .ForMember(dest => dest.CurrentCapital, opt => opt.Ignore())
                .ForMember(dest => dest.TotalInvestments, opt => opt.Ignore())
                .ForMember(dest => dest.TotalWithdrawals, opt => opt.Ignore())
                .ForMember(dest => dest.Transactions, opt => opt.Ignore());

            CreateMap<PartnerTransation, PartnerTransactionDto>().ReverseMap();
            CreateMap<PartnerTransation, PartnerTransactionResponseDto>()
                .ForMember(dest => dest.ActionDetailName, opt => opt.Ignore());

            // Item and Inventory Mappings
            CreateMap<Item, CreateItemDto>().ReverseMap();
            CreateMap<Item, Dtos.InventoryDto.UpdateItemDto>().ReverseMap();
            CreateMap<Item, ItemDto>().ReverseMap();
            CreateMap<Item, Dtos.InventoryDto.ItemResponseDto>()
                .ForMember(dest => dest.CategoryName, opt => opt.MapFrom(src => src.ItemCategory.NameEn))
                .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.ItemUnit.NameEn))
                .ForMember(dest => dest.ItemTypeName, opt => opt.Ignore())
                .ForMember(dest => dest.CurrentStock, opt => opt.Ignore())
                .ForMember(dest => dest.Images, opt => opt.MapFrom(src => src.ItemImages));

            CreateMap<ItemImage, CreateItemImageDto>().ReverseMap();
            CreateMap<ItemImage, ItemImageResponseDto>();

            CreateMap<InventoryTransaction, CreateInventoryTransactionDto>().ReverseMap();
            CreateMap<InventoryTransaction, InventoryTransactionResponseDto>()
                .ForMember(dest => dest.InvoiceNumber, opt => opt.Ignore())
                .ForMember(dest => dest.ActionTypeName, opt => opt.MapFrom(src => src.MainAction.NameEn))
                .ForMember(dest => dest.ItemName, opt => opt.MapFrom(src => src.Items.NameEn))
                .ForMember(dest => dest.ItemCode, opt => opt.MapFrom(src => src.Items.ItemCode))
                .ForMember(dest => dest.UnitName, opt => opt.MapFrom(src => src.Units.NameEn));

            // Category Mappings
            CreateMap<ItemCategory, ItemCategory>().ReverseMap();
            CreateMap<CategoryType, CategoryType>().ReverseMap();
            CreateMap<Unit, Unit>().ReverseMap();

            // Financial Mappings
            CreateMap<Treasury, CreateTreasuryDto>().ReverseMap();
            CreateMap<Treasury, UpdateTreasuryDto>().ReverseMap();
            CreateMap<Treasury, TreasuryResponseDto>()
                .ForMember(dest => dest.CurrentBalance, opt => opt.Ignore());

            CreateMap<TreasuryTransaction, CreateTreasuryTransactionDto>().ReverseMap();
            CreateMap<TreasuryTransaction, TreasuryTransactionResponseDto>()
                .ForMember(dest => dest.TreasuryName, opt => opt.MapFrom(src => src.Treasury.NameEn))
                .ForMember(dest => dest.ActionTypeName, opt => opt.MapFrom(src => src.MainAction.NameEn))
                .ForMember(dest => dest.RunningBalance, opt => opt.Ignore());

            CreateMap<FinancialTransaction, CreateFinancialTransactionDto>().ReverseMap();
            CreateMap<FinancialTransaction, FinancialTransactionResponseDto>()
                .ForMember(dest => dest.TransactionTypeName, opt => opt.MapFrom(src => src.TransactionType.NameEn));

            // Supplier/Customer Mappings
            CreateMap<SupplierCustomer, CreateSupplierCustomerDto>().ReverseMap();
            CreateMap<SupplierCustomer, UpdateSupplierCustomerDto>().ReverseMap();
            CreateMap<SupplierCustomer, SupplierCustomerResponseDto>()
                .ForMember(dest => dest.VanderTypeName, opt => opt.MapFrom(src => src.VanderType.NameEn))
                .ForMember(dest => dest.CurrentBalance, opt => opt.Ignore())
                .ForMember(dest => dest.TotalDebit, opt => opt.Ignore())
                .ForMember(dest => dest.TotalCredit, opt => opt.Ignore());

            CreateMap<SupplierCustomerTransaction, CreateSupplierCustomerTransactionDto>().ReverseMap();
            CreateMap<SupplierCustomerTransaction, SupplierCustomerTransactionResponseDto>()
                .ForMember(dest => dest.CustomerName, opt => opt.MapFrom(src => src.supplierCustomer.NameEn))
                .ForMember(dest => dest.TransactionTypeName, opt => opt.MapFrom(src => src.MainAction.NameEn))
                .ForMember(dest => dest.InvoiceNumber, opt => opt.Ignore())
                .ForMember(dest => dest.RunningBalance, opt => opt.Ignore());

            CreateMap<VanderType, CreateVanderTypeDto>().ReverseMap();
            CreateMap<VanderType, UpdateVanderTypeDto>().ReverseMap();
            CreateMap<VanderType, VanderTypeResponseDto>();

            // User Mappings
            CreateMap<User, CreateUserDto>().ReverseMap();
            CreateMap<User, UpdateUserDto>().ReverseMap();
            CreateMap<User, UserResponseDto>()
                .ForMember(dest => dest.Permissions, opt => opt.MapFrom(src => src.UserPermissions));

            CreateMap<UserPermission, CreateUserPermissionDto>().ReverseMap();
            CreateMap<UserPermission, UserPermissionResponseDto>()
                .ForMember(dest => dest.ModuleName, opt => opt.MapFrom(src => src.Module.NameEn));

            CreateMap<Module, CreateModuleDto>().ReverseMap();
            CreateMap<Module, UpdateModuleDto>().ReverseMap();
            CreateMap<Module, ModuleResponseDto>()
                .ForMember(dest => dest.UserPermissions, opt => opt.MapFrom(src => src.UserPermissions));

            // Action Mappings
            CreateMap<ActionType, ActionType>().ReverseMap();
            CreateMap<MainAction, MainAction>().ReverseMap();

            // Invoice Mappings
            CreateMap<InvoiceMaster, InvoiceMaster>().ReverseMap();
            CreateMap<InvoiceDetail, InvoiceDetail>().ReverseMap();
        }
    }
}
