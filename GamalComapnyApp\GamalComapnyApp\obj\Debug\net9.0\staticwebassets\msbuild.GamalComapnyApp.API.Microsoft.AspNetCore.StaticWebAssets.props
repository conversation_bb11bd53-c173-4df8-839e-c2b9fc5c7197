﻿<Project>
  <ItemGroup>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\profile-images\user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58.jpg'))">
      <SourceType>Package</SourceType>
      <SourceId>GamalComapnyApp.API</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/GamalComapnyApp.API</BasePath>
      <RelativePath>uploads/profile-images/user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58.jpg</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>j5cjx9y28c</Fingerprint>
      <Integrity>R+nPse577LwuH3NMKh5KP6lKRbL4pjkU/wS719K1Hcc=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\profile-images\user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58.jpg'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\users\user_2_20250624095429.png'))">
      <SourceType>Package</SourceType>
      <SourceId>GamalComapnyApp.API</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/GamalComapnyApp.API</BasePath>
      <RelativePath>uploads/users/user_2_20250624095429.png</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>2f1yanp5tc</Fingerprint>
      <Integrity>Uu6F+5YV0uGux8TgrCAOAaJq/t5k8bnm4mjYXWthKBQ=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\users\user_2_20250624095429.png'))</OriginalItemSpec>
    </StaticWebAsset>
  </ItemGroup>
</Project>