using GamalComapany.Service.Dtos;
using GamalCompany.Data.Enum;
using GamalCompany.Data.Models;

namespace GamalComapany.Service.Services
{
    /// <summary>
    /// Service responsible for ensuring ALL financial operations create corresponding FinancialTransaction records
    /// This is the central hub for financial transaction integration across all modules
    /// </summary>
    public interface IFinancialTransactionIntegrationService
    {
        #region Core Financial Transaction Creation

        /// <summary>
        /// Creates a financial transaction for any business operation
        /// This is the central method that ALL financial operations must use
        /// </summary>
        Task<ApiResponse<FinancialTransaction>> CreateFinancialTransactionAsync(
            DateTime transactionDate,
            decimal amount,
            bool isInflow,
            RefranseFinancicalEnum referenceType,
            int referenceId,
            string description,
            int? transactionTypeId = null);

        #endregion

        #region Partner Transaction Integration

        /// <summary>
        /// Creates a partner transaction with automatic financial transaction creation
        /// Ensures partner capital changes are reflected in the financial system
        /// </summary>
        Task<ApiResponse<PartnerTransactionResult>> CreatePartnerTransactionWithFinancialIntegrationAsync(
            int partnerId,
            decimal amount,
            bool isInvestment, // true = investment, false = withdrawal
            DateTime transactionDate,
            string description,
            int? treasuryId = null,
            string? notes = null,
            string? imagePath = null);

        /// <summary>
        /// Processes partner profit distribution with full financial integration
        /// </summary>
        Task<ApiResponse<List<PartnerTransactionResult>>> DistributeProfitToPartnersAsync(
            decimal totalProfit,
            DateTime distributionDate,
            string description,
            int? treasuryId = null);

        #endregion

        #region Treasury Transaction Integration

        /// <summary>
        /// Creates a treasury transaction with automatic financial transaction creation
        /// Ensures all treasury movements are tracked in the financial system
        /// </summary>
        Task<ApiResponse<TreasuryTransactionResult>> CreateTreasuryTransactionWithFinancialIntegrationAsync(
            int treasuryId,
            decimal amount,
            bool isInflow,
            DateTime transactionDate,
            string description,
            RefranseFinancicalEnum referenceType,
            int referenceId,
            string? referenceNumber = null,
            string? notes = null,
            string? imagePath = null);

        #endregion

        #region Invoice Transaction Integration

        /// <summary>
        /// Processes a complete sales invoice with inventory and financial integration
        /// </summary>
        Task<ApiResponse<InvoiceTransactionResult>> ProcessSalesInvoiceAsync(
            CreateSalesInvoiceDto invoiceDto);

        /// <summary>
        /// Processes a complete purchase invoice with inventory and financial integration
        /// </summary>
        Task<ApiResponse<InvoiceTransactionResult>> ProcessPurchaseInvoiceAsync(
            CreatePurchaseInvoiceDto invoiceDto);

        /// <summary>
        /// Processes invoice payment with treasury and financial integration
        /// </summary>
        Task<ApiResponse<PaymentTransactionResult>> ProcessInvoicePaymentAsync(
            int invoiceId,
            decimal paymentAmount,
            int treasuryId,
            DateTime paymentDate,
            string? notes = null);

        #endregion

        #region Supplier/Customer Transaction Integration

        /// <summary>
        /// Creates supplier/customer transaction with financial integration
        /// </summary>
        Task<ApiResponse<SupplierCustomerTransactionResult>> CreateSupplierCustomerTransactionWithFinancialIntegrationAsync(
            int supplierCustomerId,
            decimal amount,
            bool isDebit, // true = debit (customer payment), false = credit (supplier payment)
            DateTime transactionDate,
            string description,
            int? invoiceId = null,
            int? treasuryId = null);

        #endregion

        #region Financial Validation and Audit

        /// <summary>
        /// Validates that all transactions have corresponding financial records
        /// </summary>
        Task<ApiResponse<FinancialAuditResult>> ValidateFinancialIntegrityAsync(DateTime? fromDate = null, DateTime? toDate = null);

        /// <summary>
        /// Gets complete financial audit trail for a specific reference
        /// </summary>
        Task<ApiResponse<List<FinancialAuditTrail>>> GetFinancialAuditTrailAsync(
            RefranseFinancicalEnum referenceType,
            int referenceId);

        #endregion
    }

    #region Result DTOs

    public class PartnerTransactionResult
    {
        public PartnerTransation PartnerTransaction { get; set; } = null!;
        public FinancialTransaction FinancialTransaction { get; set; } = null!;
        public TreasuryTransaction? TreasuryTransaction { get; set; }
        public decimal NewPartnerCapital { get; set; }
        public decimal NewSharePercentage { get; set; }
    }

    public class TreasuryTransactionResult
    {
        public TreasuryTransaction TreasuryTransaction { get; set; } = null!;
        public FinancialTransaction FinancialTransaction { get; set; } = null!;
        public decimal NewTreasuryBalance { get; set; }
    }

    public class InvoiceTransactionResult
    {
        public InvoiceMaster Invoice { get; set; } = null!;
        public List<InvoiceDetail> InvoiceDetails { get; set; } = new();
        public List<InventoryTransaction> InventoryTransactions { get; set; } = new();
        public FinancialTransaction FinancialTransaction { get; set; } = null!;
        public SupplierCustomerTransaction? SupplierCustomerTransaction { get; set; }
    }

    public class PaymentTransactionResult
    {
        public SupplierCustomerTransaction SupplierCustomerTransaction { get; set; } = null!;
        public TreasuryTransaction TreasuryTransaction { get; set; } = null!;
        public FinancialTransaction FinancialTransaction { get; set; } = null!;
        public decimal NewInvoiceBalance { get; set; }
        public decimal NewCustomerBalance { get; set; }
    }

    public class SupplierCustomerTransactionResult
    {
        public SupplierCustomerTransaction SupplierCustomerTransaction { get; set; } = null!;
        public FinancialTransaction FinancialTransaction { get; set; } = null!;
        public TreasuryTransaction? TreasuryTransaction { get; set; }
        public decimal NewCustomerBalance { get; set; }
    }

    public class FinancialAuditResult
    {
        public bool IsValid { get; set; }
        public List<string> Issues { get; set; } = new();
        public int TotalTransactionsChecked { get; set; }
        public int MissingFinancialRecords { get; set; }
        public int OrphanedFinancialRecords { get; set; }
        public decimal TotalDiscrepancy { get; set; }
    }

    public class FinancialAuditTrail
    {
        public int Id { get; set; }
        public DateTime TransactionDate { get; set; }
        public string TransactionType { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public bool IsInflow { get; set; }
        public string Description { get; set; } = string.Empty;
        public RefranseFinancicalEnum ReferenceType { get; set; }
        public int ReferenceId { get; set; }
        public string? RelatedTransactions { get; set; }
    }

    #endregion

    #region Invoice DTOs

    public class CreateSalesInvoiceDto
    {
        public DateTime InvoiceDate { get; set; }
        public int CustomerId { get; set; }
        public int? DepartmentId { get; set; }
        public decimal? DiscountPercentage { get; set; }
        public decimal? TaxPercentage { get; set; }
        public string? Notes { get; set; }
        public List<CreateInvoiceDetailDto> Items { get; set; } = new();
        public PaymentInfo? PaymentInfo { get; set; }
    }

    public class CreatePurchaseInvoiceDto
    {
        public DateTime InvoiceDate { get; set; }
        public int SupplierId { get; set; }
        public int? DepartmentId { get; set; }
        public decimal? DiscountPercentage { get; set; }
        public decimal? TaxPercentage { get; set; }
        public string? Notes { get; set; }
        public List<CreateInvoiceDetailDto> Items { get; set; } = new();
        public PaymentInfo? PaymentInfo { get; set; }
    }

    public class CreateInvoiceDetailDto
    {
        public int ItemId { get; set; }
        public int UnitId { get; set; }
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public string? Notes { get; set; }
    }

    public class PaymentInfo
    {
        public decimal PaidAmount { get; set; }
        public int? TreasuryId { get; set; }
        public string? PaymentNotes { get; set; }
    }

    #endregion
}
