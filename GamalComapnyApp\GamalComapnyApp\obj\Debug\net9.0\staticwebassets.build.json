{"Version": 1, "Hash": "P+9ay0pOFHBXEPcjsMb+xxiKE+fQL5sv+BeHAtUCUZI=", "Source": "GamalComapnyApp.API", "BasePath": "_content/GamalComapnyApp.API", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "GamalComapnyApp.API\\wwwroot", "Source": "GamalComapnyApp.API", "ContentRoot": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\wwwroot\\", "BasePath": "_content/GamalComapnyApp.API", "Pattern": "**"}], "Assets": [{"Identity": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\wwwroot\\uploads\\profile-images\\user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58.jpg", "SourceId": "GamalComapnyApp.API", "SourceType": "Discovered", "ContentRoot": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\wwwroot\\", "BasePath": "_content/GamalComapnyApp.API", "RelativePath": "uploads/profile-images/user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "j5cjx9y28c", "Integrity": "R+nPse577LwuH3NMKh5KP6lKRbL4pjkU/wS719K1Hcc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile-images\\user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58.jpg"}, {"Identity": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\wwwroot\\uploads\\users\\user_2_20250624095429.png", "SourceId": "GamalComapnyApp.API", "SourceType": "Discovered", "ContentRoot": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\wwwroot\\", "BasePath": "_content/GamalComapnyApp.API", "RelativePath": "uploads/users/user_2_20250624095429#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2f1yanp5tc", "Integrity": "Uu6F+5YV0uGux8TgrCAOAaJq/t5k8bnm4mjYXWthKBQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\users\\user_2_20250624095429.png"}], "Endpoints": [{"Route": "uploads/profile-images/user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58.j5cjx9y28c.jpg", "AssetFile": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\wwwroot\\uploads\\profile-images\\user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "178920"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"R+nPse577LwuH3NMKh5KP6lKRbL4pjkU/wS719K1Hcc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 12:32:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5cjx9y28c"}, {"Name": "label", "Value": "uploads/profile-images/user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58.jpg"}, {"Name": "integrity", "Value": "sha256-R+nPse577LwuH3NMKh5KP6lKRbL4pjkU/wS719K1Hcc="}]}, {"Route": "uploads/profile-images/user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58.jpg", "AssetFile": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\wwwroot\\uploads\\profile-images\\user_2_a18629d0-6fb6-494b-bda4-36e5e3626e58.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "178920"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"R+nPse577LwuH3NMKh5KP6lKRbL4pjkU/wS719K1Hcc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 12:32:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-R+nPse577LwuH3NMKh5KP6lKRbL4pjkU/wS719K1Hcc="}]}, {"Route": "uploads/users/user_2_20250624095429.2f1yanp5tc.png", "AssetFile": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\wwwroot\\uploads\\users\\user_2_20250624095429.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "338483"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Uu6F+5YV0uGux8TgrCAOAaJq/t5k8bnm4mjYXWthKBQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 09:54:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2f1yanp5tc"}, {"Name": "label", "Value": "uploads/users/user_2_20250624095429.png"}, {"Name": "integrity", "Value": "sha256-Uu6F+5YV0uGux8TgrCAOAaJq/t5k8bnm4mjYXWthKBQ="}]}, {"Route": "uploads/users/user_2_20250624095429.png", "AssetFile": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\wwwroot\\uploads\\users\\user_2_20250624095429.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "338483"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Uu6F+5YV0uGux8TgrCAOAaJq/t5k8bnm4mjYXWthKBQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 09:54:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Uu6F+5YV0uGux8TgrCAOAaJq/t5k8bnm4mjYXWthKBQ="}]}]}