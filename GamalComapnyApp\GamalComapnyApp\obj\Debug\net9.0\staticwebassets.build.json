{"Version": 1, "Hash": "1DPqi8lIiwJJJCXjhQMofIuSWgYQxsf9SpvoewgJano=", "Source": "GamalComapnyApp.API", "BasePath": "_content/GamalComapnyApp.API", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "GamalComapnyApp.API\\wwwroot", "Source": "GamalComapnyApp.API", "ContentRoot": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\wwwroot\\", "BasePath": "_content/GamalComapnyApp.API", "Pattern": "**"}], "Assets": [{"Identity": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\wwwroot\\uploads\\users\\user_2_20250624095429.png", "SourceId": "GamalComapnyApp.API", "SourceType": "Discovered", "ContentRoot": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\wwwroot\\", "BasePath": "_content/GamalComapnyApp.API", "RelativePath": "uploads/users/user_2_20250624095429#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2f1yanp5tc", "Integrity": "Uu6F+5YV0uGux8TgrCAOAaJq/t5k8bnm4mjYXWthKBQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\users\\user_2_20250624095429.png"}], "Endpoints": [{"Route": "uploads/users/user_2_20250624095429.2f1yanp5tc.png", "AssetFile": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\wwwroot\\uploads\\users\\user_2_20250624095429.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "338483"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Uu6F+5YV0uGux8TgrCAOAaJq/t5k8bnm4mjYXWthKBQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 09:54:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2f1yanp5tc"}, {"Name": "label", "Value": "uploads/users/user_2_20250624095429.png"}, {"Name": "integrity", "Value": "sha256-Uu6F+5YV0uGux8TgrCAOAaJq/t5k8bnm4mjYXWthKBQ="}]}, {"Route": "uploads/users/user_2_20250624095429.png", "AssetFile": "D:\\AIProjectTest\\Sourcs\\GamalCompanyApp\\GamalComapnyApp\\GamalComapnyApp\\wwwroot\\uploads\\users\\user_2_20250624095429.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "338483"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Uu6F+5YV0uGux8TgrCAOAaJq/t5k8bnm4mjYXWthKBQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 09:54:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Uu6F+5YV0uGux8TgrCAOAaJq/t5k8bnm4mjYXWthKBQ="}]}]}