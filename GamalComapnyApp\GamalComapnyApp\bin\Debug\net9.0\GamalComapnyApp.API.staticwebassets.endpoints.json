{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "uploads/users/user_2_20250624095429.2f1yanp5tc.png", "AssetFile": "uploads/users/user_2_20250624095429.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "338483"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Uu6F+5YV0uGux8TgrCAOAaJq/t5k8bnm4mjYXWthKBQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 09:54:29 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2f1yanp5tc"}, {"Name": "integrity", "Value": "sha256-Uu6F+5YV0uGux8TgrCAOAaJq/t5k8bnm4mjYXWthKBQ="}, {"Name": "label", "Value": "uploads/users/user_2_20250624095429.png"}]}, {"Route": "uploads/users/user_2_20250624095429.png", "AssetFile": "uploads/users/user_2_20250624095429.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "338483"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Uu6F+5YV0uGux8TgrCAOAaJq/t5k8bnm4mjYXWthKBQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 09:54:29 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Uu6F+5YV0uGux8TgrCAOAaJq/t5k8bnm4mjYXWthKBQ="}]}]}