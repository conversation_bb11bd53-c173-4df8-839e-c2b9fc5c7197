2025-06-25 13:07:04.812 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-25 13:07:05.472 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-25 13:07:05.583 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 13:07:05.585 +03:00 [INF] Hosting environment: Development
2025-06-25 13:07:05.586 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-25 13:07:29.334 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-25 13:07:29.672 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 348.4305ms
2025-06-25 13:07:47.852 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner/capital-calculation - null null
2025-06-25 13:07:49.110 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '25/06/2025 10:07:49 ص'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-25 13:07:49.117 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '25/06/2025 10:07:49 ص'.
2025-06-25 13:07:49.131 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '25/06/2025 10:07:49 ص'.
2025-06-25 13:07:49.152 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-25 13:07:49.153 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-25 13:07:49.158 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-25 13:07:49.161 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner/capital-calculation - 401 0 null 1308.9231ms
2025-06-25 13:08:14.270 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/login - application/json 52
2025-06-25 13:08:14.278 +03:00 [INF] CORS policy execution successful.
2025-06-25 13:08:14.283 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '25/06/2025 10:08:14 ص'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-25 13:08:14.287 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '25/06/2025 10:08:14 ص'.
2025-06-25 13:08:14.288 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '25/06/2025 10:08:14 ص'.
2025-06-25 13:08:14.291 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-25 13:08:14.327 +03:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(GamalComapany.Service.Dtos.UserDto.LoginDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-25 13:08:18.145 +03:00 [INF] Executed DbCommand (185ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-25 13:08:18.332 +03:00 [INF] Access token generated for user 2
2025-06-25 13:08:18.571 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-06-25 13:08:18.599 +03:00 [INF] User logged in successfully: Admin
2025-06-25 13:08:18.608 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.LoginResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 13:08:18.666 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API) in 4331.2128ms
2025-06-25 13:08:18.670 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-25 13:08:18.679 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/login - 200 null application/json; charset=utf-8 4409.207ms
2025-06-25 13:08:51.434 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner/capital-calculation - null null
2025-06-25 13:08:51.441 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '25/06/2025 10:08:51 ص'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-25 13:08:51.445 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '25/06/2025 10:08:51 ص'.
2025-06-25 13:08:51.446 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '25/06/2025 10:08:51 ص'.
2025-06-25 13:08:51.448 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-25 13:08:51.449 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-25 13:08:51.451 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-25 13:08:51.453 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner/capital-calculation - 401 0 null 18.9273ms
2025-06-25 13:08:53.619 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner/capital-calculation - null null
2025-06-25 13:08:53.623 +03:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '25/06/2025 10:08:53 ص'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-25 13:08:53.626 +03:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '25/06/2025 10:08:53 ص'.
2025-06-25 13:08:53.627 +03:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '24/06/2025 04:32:26 م', Current time (UTC): '25/06/2025 10:08:53 ص'.
2025-06-25 13:08:53.646 +03:00 [WRN] Authorization failed: User not authenticated
2025-06-25 13:08:53.647 +03:00 [INF] Authorization failed. Fail() was explicitly called.
2025-06-25 13:08:53.648 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-25 13:08:53.650 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner/capital-calculation - 401 0 null 31.1424ms
2025-06-25 13:09:30.725 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner/capital-calculation - null null
2025-06-25 13:09:30.761 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CalculatePartnersCapital (GamalComapnyApp.API)'
2025-06-25 13:09:30.766 +03:00 [INF] Route matched with {action = "CalculatePartnersCapital", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CalculatePartnersCapital() on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-25 13:09:30.871 +03:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-06-25 13:09:30.884 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.PartnerDto.PartnerCapitalCalculationDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 13:09:30.897 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.CalculatePartnersCapital (GamalComapnyApp.API) in 128.5487ms
2025-06-25 13:09:30.900 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CalculatePartnersCapital (GamalComapnyApp.API)'
2025-06-25 13:09:30.902 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner/capital-calculation - 200 null application/json; charset=utf-8 181.1657ms
2025-06-25 13:15:59.670 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner/1/transactions - null null
2025-06-25 13:15:59.675 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.GetPartnerTransactions (GamalComapnyApp.API)'
2025-06-25 13:15:59.680 +03:00 [INF] Route matched with {action = "GetPartnerTransactions", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetPartnerTransactions(Int32) on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-25 13:15:59.869 +03:00 [INF] Executed DbCommand (28ms) [Parameters=[@__partnerId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[ActionDetailId], [p].[Amount], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[ImagePath], [p].[IsActive], [p].[IsDeleted], [p].[Notes], [p].[PartnerId], [p].[TransactionDate], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [PartnerTransations] AS [p]
WHERE [p].[PartnerId] = @__partnerId_0 AND [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[TransactionDate] DESC
2025-06-25 13:15:59.884 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.PartnerDto.PartnerTransactionResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 13:15:59.890 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.GetPartnerTransactions (GamalComapnyApp.API) in 207.6804ms
2025-06-25 13:15:59.893 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.GetPartnerTransactions (GamalComapnyApp.API)'
2025-06-25 13:15:59.895 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner/1/transactions - 200 null application/json; charset=utf-8 225.0091ms
2025-06-25 13:16:38.012 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner - null null
2025-06-25 13:16:38.034 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.GetAllPartners (GamalComapnyApp.API)'
2025-06-25 13:16:38.040 +03:00 [INF] Route matched with {action = "GetAllPartners", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllPartners() on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-25 13:16:38.058 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-06-25 13:16:38.071 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.PartnerDto.PartnerResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 13:16:38.086 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.GetAllPartners (GamalComapnyApp.API) in 41.9467ms
2025-06-25 13:16:38.088 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.GetAllPartners (GamalComapnyApp.API)'
2025-06-25 13:16:38.090 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner - 200 null application/json; charset=utf-8 81.1313ms
2025-06-25 13:18:54.864 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Partner/capital-calculation - null null
2025-06-25 13:18:54.871 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CalculatePartnersCapital (GamalComapnyApp.API)'
2025-06-25 13:18:54.889 +03:00 [INF] Route matched with {action = "CalculatePartnersCapital", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CalculatePartnersCapital() on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-25 13:18:54.911 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy], [p0].[Id], [p0].[ActionDetailId], [p0].[Amount], [p0].[CreatedAt], [p0].[CreatedBy], [p0].[Description], [p0].[ImagePath], [p0].[IsActive], [p0].[IsDeleted], [p0].[Notes], [p0].[PartnerId], [p0].[TransactionDate], [p0].[UpdatedAt], [p0].[UpdatedBy]
FROM [Partners] AS [p]
LEFT JOIN [PartnerTransations] AS [p0] ON [p].[Id] = [p0].[PartnerId]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
ORDER BY [p].[Id]
2025-06-25 13:18:54.914 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.PartnerDto.PartnerCapitalCalculationDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 13:18:54.915 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.CalculatePartnersCapital (GamalComapnyApp.API) in 23.2541ms
2025-06-25 13:18:54.917 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CalculatePartnersCapital (GamalComapnyApp.API)'
2025-06-25 13:18:54.918 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Partner/capital-calculation - 200 null application/json; charset=utf-8 56.3639ms
2025-06-25 13:20:20.265 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/Partner/profit-distribution - application/json 1
2025-06-25 13:20:20.290 +03:00 [INF] CORS policy execution successful.
2025-06-25 13:20:20.292 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CalculateProfitDistribution (GamalComapnyApp.API)'
2025-06-25 13:20:20.297 +03:00 [INF] Route matched with {action = "CalculateProfitDistribution", controller = "Partner"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CalculateProfitDistribution(System.Decimal) on controller GamalComapnyApp.API.Controllers.PartnerController (GamalComapnyApp.API).
2025-06-25 13:20:20.329 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[InitialInvestment], [p].[IsActive], [p].[IsDeleted], [p].[NameEn], [p].[SharePercentage], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Partners] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit) AND [p].[IsActive] = CAST(1 AS bit)
2025-06-25 13:20:20.334 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.PartnerDto.PartnerProfitDistributionDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 13:20:20.339 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.PartnerController.CalculateProfitDistribution (GamalComapnyApp.API) in 39.9281ms
2025-06-25 13:20:20.341 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.PartnerController.CalculateProfitDistribution (GamalComapnyApp.API)'
2025-06-25 13:20:20.342 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/Partner/profit-distribution - 200 null application/json; charset=utf-8 79.7119ms
2025-06-25 14:04:05.245 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-25 14:04:05.578 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-25 14:04:05.651 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 14:04:05.653 +03:00 [INF] Hosting environment: Development
2025-06-25 14:04:05.654 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-25 14:36:49.617 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-25 14:36:49.922 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-25 14:36:49.989 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 14:36:49.991 +03:00 [INF] Hosting environment: Development
2025-06-25 14:36:49.992 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-25 14:36:52.584 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-25 14:36:52.919 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 341.7096ms
2025-06-25 14:36:52.995 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_vs/browserLink - null null
2025-06-25 14:36:52.995 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - null null
2025-06-25 14:36:53.044 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - 200 16523 application/javascript; charset=utf-8 48.9326ms
2025-06-25 14:36:53.094 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_vs/browserLink - 200 null text/javascript; charset=UTF-8 98.7921ms
2025-06-25 14:36:53.237 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-25 14:36:53.416 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 178.6673ms
2025-06-25 14:37:55.358 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5250/api/User/login - application/json 52
2025-06-25 14:37:55.402 +03:00 [INF] CORS policy execution successful.
2025-06-25 14:37:55.466 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-25 14:37:55.490 +03:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(GamalComapany.Service.Dtos.UserDto.LoginDto) on controller GamalComapnyApp.API.Controllers.UserController (GamalComapnyApp.API).
2025-06-25 14:37:57.700 +03:00 [INF] Executed DbCommand (74ms) [Parameters=[@__loginDto_UserName_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [u1].[Id], [u1].[CreatedAt], [u1].[CreatedBy], [u1].[FullName], [u1].[IsActive], [u1].[IsDeleted], [u1].[Password], [u1].[Phone], [u1].[ProfileImage], [u1].[UpdatedAt], [u1].[UpdatedBy], [u1].[UserName], [s].[Id], [s].[CreatedAt], [s].[CreatedBy], [s].[IsActive], [s].[IsDeleted], [s].[ModuleId], [s].[Permission], [s].[UpdatedAt], [s].[UpdatedBy], [s].[UserId], [s].[Id0], [s].[CreatedAt0], [s].[CreatedBy0], [s].[Description], [s].[IsActive0], [s].[IsDeleted0], [s].[NameEn], [s].[UpdatedAt0], [s].[UpdatedBy0]
FROM (
    SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[FullName], [u].[IsActive], [u].[IsDeleted], [u].[Password], [u].[Phone], [u].[ProfileImage], [u].[UpdatedAt], [u].[UpdatedBy], [u].[UserName]
    FROM [Users] AS [u]
    WHERE [u].[UserName] = @__loginDto_UserName_0 AND [u].[IsDeleted] = CAST(0 AS bit)
) AS [u1]
LEFT JOIN (
    SELECT [u0].[Id], [u0].[CreatedAt], [u0].[CreatedBy], [u0].[IsActive], [u0].[IsDeleted], [u0].[ModuleId], [u0].[Permission], [u0].[UpdatedAt], [u0].[UpdatedBy], [u0].[UserId], [m].[Id] AS [Id0], [m].[CreatedAt] AS [CreatedAt0], [m].[CreatedBy] AS [CreatedBy0], [m].[Description], [m].[IsActive] AS [IsActive0], [m].[IsDeleted] AS [IsDeleted0], [m].[NameEn], [m].[UpdatedAt] AS [UpdatedAt0], [m].[UpdatedBy] AS [UpdatedBy0]
    FROM [UserPermissions] AS [u0]
    INNER JOIN [Modules] AS [m] ON [u0].[ModuleId] = [m].[Id]
) AS [s] ON [u1].[Id] = [s].[UserId]
ORDER BY [u1].[Id], [s].[Id]
2025-06-25 14:37:57.875 +03:00 [INF] Access token generated for user 2
2025-06-25 14:37:58.066 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Boolean), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 4000), @p10='?' (DbType = DateTime2), @p11='?' (DbType = Int32), @p12='?' (Size = 4000), @p13='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [RefreshTokens] ([CreatedAt], [CreatedBy], [ExpiryDate], [IpAddress], [IsActive], [IsDeleted], [IsRevoked], [RevokedAt], [RevokedReason], [Token], [UpdatedAt], [UpdatedBy], [UserAgent], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
2025-06-25 14:37:58.091 +03:00 [INF] User logged in successfully: Admin
2025-06-25 14:37:58.103 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.UserDto.LoginResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 14:37:58.138 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API) in 2643.0619ms
2025-06-25 14:37:58.140 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.UserController.Login (GamalComapnyApp.API)'
2025-06-25 14:37:58.148 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5250/api/User/login - 200 null application/json; charset=utf-8 2790.3496ms
2025-06-25 14:39:11.018 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Financial/treasuries - null null
2025-06-25 14:39:11.079 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetAllTreasuries (GamalComapnyApp.API)'
2025-06-25 14:39:11.086 +03:00 [INF] Route matched with {action = "GetAllTreasuries", controller = "Financial"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllTreasuries() on controller GamalComapnyApp.API.Controllers.FinancialController (GamalComapnyApp.API).
2025-06-25 14:39:11.168 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[CreatedAt], [t].[CreatedBy], [t].[Description], [t].[IsActive], [t].[IsDeleted], [t].[NameEn], [t].[UpdatedAt], [t].[UpdatedBy]
FROM [Treasuries] AS [t]
WHERE [t].[IsDeleted] = CAST(0 AS bit) AND [t].[IsActive] = CAST(1 AS bit)
2025-06-25 14:39:11.185 +03:00 [INF] Retrieved 1 treasuries
2025-06-25 14:39:11.187 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.FinancialDto.TreasuryResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 14:39:11.200 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.FinancialController.GetAllTreasuries (GamalComapnyApp.API) in 112.1816ms
2025-06-25 14:39:11.203 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetAllTreasuries (GamalComapnyApp.API)'
2025-06-25 14:39:11.204 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Financial/treasuries - 200 null application/json; charset=utf-8 188.5336ms
2025-06-25 14:39:12.899 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Financial/treasuries - null null
2025-06-25 14:39:12.906 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetAllTreasuries (GamalComapnyApp.API)'
2025-06-25 14:39:12.909 +03:00 [INF] Route matched with {action = "GetAllTreasuries", controller = "Financial"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllTreasuries() on controller GamalComapnyApp.API.Controllers.FinancialController (GamalComapnyApp.API).
2025-06-25 14:39:12.935 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[CreatedAt], [t].[CreatedBy], [t].[Description], [t].[IsActive], [t].[IsDeleted], [t].[NameEn], [t].[UpdatedAt], [t].[UpdatedBy]
FROM [Treasuries] AS [t]
WHERE [t].[IsDeleted] = CAST(0 AS bit) AND [t].[IsActive] = CAST(1 AS bit)
2025-06-25 14:39:12.939 +03:00 [INF] Retrieved 1 treasuries
2025-06-25 14:39:12.940 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.FinancialDto.TreasuryResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 14:39:12.941 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.FinancialController.GetAllTreasuries (GamalComapnyApp.API) in 29.9112ms
2025-06-25 14:39:12.943 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetAllTreasuries (GamalComapnyApp.API)'
2025-06-25 14:39:12.944 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Financial/treasuries - 200 null application/json; charset=utf-8 45.465ms
2025-06-25 14:41:59.897 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Financial/treasuries/1 - null null
2025-06-25 14:41:59.903 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetTreasuryById (GamalComapnyApp.API)'
2025-06-25 14:41:59.911 +03:00 [INF] Route matched with {action = "GetTreasuryById", controller = "Financial"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTreasuryById(Int32) on controller GamalComapnyApp.API.Controllers.FinancialController (GamalComapnyApp.API).
2025-06-25 14:41:59.974 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[CreatedAt], [t].[CreatedBy], [t].[Description], [t].[IsActive], [t].[IsDeleted], [t].[NameEn], [t].[UpdatedAt], [t].[UpdatedBy]
FROM [Treasuries] AS [t]
WHERE [t].[Id] = @__p_0
2025-06-25 14:41:59.999 +03:00 [INF] Executing OkObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.FinancialDto.TreasuryResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 14:42:00.001 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.FinancialController.GetTreasuryById (GamalComapnyApp.API) in 75.6457ms
2025-06-25 14:42:00.003 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetTreasuryById (GamalComapnyApp.API)'
2025-06-25 14:42:00.005 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Financial/treasuries/1 - 200 null application/json; charset=utf-8 108.3737ms
2025-06-25 14:43:03.109 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Financial/treasury-transactions?treasuryId=1&fromDate=2024-01-01&toDate=2025-07-01 - null null
2025-06-25 14:43:03.113 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetTreasuryTransactions (GamalComapnyApp.API)'
2025-06-25 14:43:03.118 +03:00 [INF] Route matched with {action = "GetTreasuryTransactions", controller = "Financial"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTreasuryTransactions(System.Nullable`1[System.Int32], System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime]) on controller GamalComapnyApp.API.Controllers.FinancialController (GamalComapnyApp.API).
2025-06-25 14:43:03.193 +03:00 [ERR] Failed executing DbCommand (28ms) [Parameters=[@__treasuryId_Value_0='?' (DbType = Int32), @__fromDate_Value_1='?' (DbType = DateTime2), @__toDate_Value_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[ActionTypeId], [t].[Amount], [t].[CreatedAt], [t].[CreatedBy], [t].[Description], [t].[FinancialTransactionId], [t].[ImagePath], [t].[IsActive], [t].[IsDeleted], [t].[Notes], [t].[ReferenceId], [t].[ReferenceNumber], [t].[ReferenceType], [t].[TransactionDate], [t].[TreasuryId], [t].[UpdatedAt], [t].[UpdatedBy], [t0].[Id], [t0].[CreatedAt], [t0].[CreatedBy], [t0].[Description], [t0].[IsActive], [t0].[IsDeleted], [t0].[NameEn], [t0].[UpdatedAt], [t0].[UpdatedBy], [m].[Id], [m].[ActionTypeId], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[IsActive], [m].[IsDeleted], [m].[NameEn], [m].[ParentActionId], [m].[UpdatedAt], [m].[UpdatedBy], [a].[Id], [a].[CreatedAt], [a].[CreatedBy], [a].[Description], [a].[IsActive], [a].[IsDeleted], [a].[NameEn], [a].[UpdatedAt], [a].[UpdatedBy]
FROM [TreasuryTransactions] AS [t]
INNER JOIN [Treasuries] AS [t0] ON [t].[TreasuryId] = [t0].[Id]
INNER JOIN [MainActions] AS [m] ON [t].[ActionTypeId] = [m].[Id]
LEFT JOIN [ActionTypes] AS [a] ON [m].[ActionTypeId] = [a].[Id]
WHERE [t].[IsDeleted] = CAST(0 AS bit) AND [t].[TreasuryId] = @__treasuryId_Value_0 AND [t].[TransactionDate] >= @__fromDate_Value_1 AND [t].[TransactionDate] <= @__toDate_Value_2
ORDER BY [t].[TransactionDate] DESC, [t].[Id] DESC
2025-06-25 14:43:03.215 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'GamalCompany.Data.Context.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'ReferenceId'.
Invalid column name 'ReferenceType'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:e061c3e8-92d0-4b9f-b2dc-c07b0e2bb424
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'ReferenceId'.
Invalid column name 'ReferenceType'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:e061c3e8-92d0-4b9f-b2dc-c07b0e2bb424
Error Number:207,State:1,Class:16
2025-06-25 14:43:03.362 +03:00 [ERR] Error retrieving treasury transactions
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'ReferenceId'.
Invalid column name 'ReferenceType'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at GamalComapany.Service.Repositories.Implementations.FinancialService.GetTreasuryTransactionsAsync(Nullable`1 treasuryId, Nullable`1 fromDate, Nullable`1 toDate) in D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapany.Service\Repositories\Implementations\FinancialService.cs:line 200
ClientConnectionId:e061c3e8-92d0-4b9f-b2dc-c07b0e2bb424
Error Number:207,State:1,Class:16
2025-06-25 14:43:03.395 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.FinancialDto.TreasuryTransactionResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 14:43:03.399 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.FinancialController.GetTreasuryTransactions (GamalComapnyApp.API) in 279.3084ms
2025-06-25 14:43:03.402 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetTreasuryTransactions (GamalComapnyApp.API)'
2025-06-25 14:43:03.403 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Financial/treasury-transactions?treasuryId=1&fromDate=2024-01-01&toDate=2025-07-01 - 400 null application/json; charset=utf-8 296.2717ms
2025-06-25 14:43:13.612 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Financial/treasury-transactions?treasuryId=1&fromDate=2024-01-01&toDate=2025-07-01 - null null
2025-06-25 14:43:13.616 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetTreasuryTransactions (GamalComapnyApp.API)'
2025-06-25 14:43:13.617 +03:00 [INF] Route matched with {action = "GetTreasuryTransactions", controller = "Financial"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTreasuryTransactions(System.Nullable`1[System.Int32], System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime]) on controller GamalComapnyApp.API.Controllers.FinancialController (GamalComapnyApp.API).
2025-06-25 14:43:13.644 +03:00 [ERR] Failed executing DbCommand (22ms) [Parameters=[@__treasuryId_Value_0='?' (DbType = Int32), @__fromDate_Value_1='?' (DbType = DateTime2), @__toDate_Value_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[ActionTypeId], [t].[Amount], [t].[CreatedAt], [t].[CreatedBy], [t].[Description], [t].[FinancialTransactionId], [t].[ImagePath], [t].[IsActive], [t].[IsDeleted], [t].[Notes], [t].[ReferenceId], [t].[ReferenceNumber], [t].[ReferenceType], [t].[TransactionDate], [t].[TreasuryId], [t].[UpdatedAt], [t].[UpdatedBy], [t0].[Id], [t0].[CreatedAt], [t0].[CreatedBy], [t0].[Description], [t0].[IsActive], [t0].[IsDeleted], [t0].[NameEn], [t0].[UpdatedAt], [t0].[UpdatedBy], [m].[Id], [m].[ActionTypeId], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[IsActive], [m].[IsDeleted], [m].[NameEn], [m].[ParentActionId], [m].[UpdatedAt], [m].[UpdatedBy], [a].[Id], [a].[CreatedAt], [a].[CreatedBy], [a].[Description], [a].[IsActive], [a].[IsDeleted], [a].[NameEn], [a].[UpdatedAt], [a].[UpdatedBy]
FROM [TreasuryTransactions] AS [t]
INNER JOIN [Treasuries] AS [t0] ON [t].[TreasuryId] = [t0].[Id]
INNER JOIN [MainActions] AS [m] ON [t].[ActionTypeId] = [m].[Id]
LEFT JOIN [ActionTypes] AS [a] ON [m].[ActionTypeId] = [a].[Id]
WHERE [t].[IsDeleted] = CAST(0 AS bit) AND [t].[TreasuryId] = @__treasuryId_Value_0 AND [t].[TransactionDate] >= @__fromDate_Value_1 AND [t].[TransactionDate] <= @__toDate_Value_2
ORDER BY [t].[TransactionDate] DESC, [t].[Id] DESC
2025-06-25 14:43:13.649 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'GamalCompany.Data.Context.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'ReferenceId'.
Invalid column name 'ReferenceType'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:e061c3e8-92d0-4b9f-b2dc-c07b0e2bb424
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'ReferenceId'.
Invalid column name 'ReferenceType'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:e061c3e8-92d0-4b9f-b2dc-c07b0e2bb424
Error Number:207,State:1,Class:16
2025-06-25 14:43:13.718 +03:00 [ERR] Error retrieving treasury transactions
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'ReferenceId'.
Invalid column name 'ReferenceType'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at GamalComapany.Service.Repositories.Implementations.FinancialService.GetTreasuryTransactionsAsync(Nullable`1 treasuryId, Nullable`1 fromDate, Nullable`1 toDate) in D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapany.Service\Repositories\Implementations\FinancialService.cs:line 200
ClientConnectionId:e061c3e8-92d0-4b9f-b2dc-c07b0e2bb424
Error Number:207,State:1,Class:16
2025-06-25 14:43:13.727 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[GamalComapany.Service.Dtos.FinancialDto.TreasuryTransactionResponseDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 14:43:13.728 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.FinancialController.GetTreasuryTransactions (GamalComapnyApp.API) in 109.816ms
2025-06-25 14:43:13.730 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetTreasuryTransactions (GamalComapnyApp.API)'
2025-06-25 14:43:13.731 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Financial/treasury-transactions?treasuryId=1&fromDate=2024-01-01&toDate=2025-07-01 - 400 null application/json; charset=utf-8 119.3204ms
2025-06-25 14:44:55.304 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Financial/treasuries/1/balance?fromDate=2024-01-01&toDate=2026-01-01 - null null
2025-06-25 14:44:55.312 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetTreasuryBalance (GamalComapnyApp.API)'
2025-06-25 14:44:55.320 +03:00 [INF] Route matched with {action = "GetTreasuryBalance", controller = "Financial"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTreasuryBalance(Int32, System.DateTime, System.DateTime) on controller GamalComapnyApp.API.Controllers.FinancialController (GamalComapnyApp.API).
2025-06-25 14:44:55.340 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[CreatedAt], [t].[CreatedBy], [t].[Description], [t].[IsActive], [t].[IsDeleted], [t].[NameEn], [t].[UpdatedAt], [t].[UpdatedBy]
FROM [Treasuries] AS [t]
WHERE [t].[Id] = @__p_0
2025-06-25 14:44:55.364 +03:00 [ERR] Failed executing DbCommand (9ms) [Parameters=[@__treasuryId_0='?' (DbType = Int32), @__fromDate_1='?' (DbType = DateTime2), @__toDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[ActionTypeId], [t].[Amount], [t].[CreatedAt], [t].[CreatedBy], [t].[Description], [t].[FinancialTransactionId], [t].[ImagePath], [t].[IsActive], [t].[IsDeleted], [t].[Notes], [t].[ReferenceId], [t].[ReferenceNumber], [t].[ReferenceType], [t].[TransactionDate], [t].[TreasuryId], [t].[UpdatedAt], [t].[UpdatedBy], [m].[Id], [m].[ActionTypeId], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[IsActive], [m].[IsDeleted], [m].[NameEn], [m].[ParentActionId], [m].[UpdatedAt], [m].[UpdatedBy], [a].[Id], [a].[CreatedAt], [a].[CreatedBy], [a].[Description], [a].[IsActive], [a].[IsDeleted], [a].[NameEn], [a].[UpdatedAt], [a].[UpdatedBy]
FROM [TreasuryTransactions] AS [t]
INNER JOIN [MainActions] AS [m] ON [t].[ActionTypeId] = [m].[Id]
LEFT JOIN [ActionTypes] AS [a] ON [m].[ActionTypeId] = [a].[Id]
WHERE [t].[TreasuryId] = @__treasuryId_0 AND [t].[IsDeleted] = CAST(0 AS bit) AND [t].[TransactionDate] >= @__fromDate_1 AND [t].[TransactionDate] <= @__toDate_2
2025-06-25 14:44:55.368 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'GamalCompany.Data.Context.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'ReferenceId'.
Invalid column name 'ReferenceType'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:e061c3e8-92d0-4b9f-b2dc-c07b0e2bb424
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'ReferenceId'.
Invalid column name 'ReferenceType'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:e061c3e8-92d0-4b9f-b2dc-c07b0e2bb424
Error Number:207,State:1,Class:16
2025-06-25 14:44:55.433 +03:00 [ERR] Error calculating treasury balance: 1
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'ReferenceId'.
Invalid column name 'ReferenceType'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at GamalComapany.Service.Repositories.Implementations.FinancialService.GetTreasuryBalanceAsync(Int32 treasuryId, DateTime fromDate, DateTime toDate) in D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapany.Service\Repositories\Implementations\FinancialService.cs:line 429
ClientConnectionId:e061c3e8-92d0-4b9f-b2dc-c07b0e2bb424
Error Number:207,State:1,Class:16
2025-06-25 14:44:55.441 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.FinancialDto.TreasuryBalanceDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 14:44:55.446 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.FinancialController.GetTreasuryBalance (GamalComapnyApp.API) in 123.8985ms
2025-06-25 14:44:55.448 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetTreasuryBalance (GamalComapnyApp.API)'
2025-06-25 14:44:55.450 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Financial/treasuries/1/balance?fromDate=2024-01-01&toDate=2026-01-01 - 400 null application/json; charset=utf-8 149.1109ms
2025-06-25 14:45:00.255 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/api/Financial/treasuries/1/balance?fromDate=2024-01-01&toDate=2026-01-01 - null null
2025-06-25 14:45:00.259 +03:00 [INF] Executing endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetTreasuryBalance (GamalComapnyApp.API)'
2025-06-25 14:45:00.261 +03:00 [INF] Route matched with {action = "GetTreasuryBalance", controller = "Financial"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTreasuryBalance(Int32, System.DateTime, System.DateTime) on controller GamalComapnyApp.API.Controllers.FinancialController (GamalComapnyApp.API).
2025-06-25 14:45:00.265 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[CreatedAt], [t].[CreatedBy], [t].[Description], [t].[IsActive], [t].[IsDeleted], [t].[NameEn], [t].[UpdatedAt], [t].[UpdatedBy]
FROM [Treasuries] AS [t]
WHERE [t].[Id] = @__p_0
2025-06-25 14:45:00.271 +03:00 [ERR] Failed executing DbCommand (3ms) [Parameters=[@__treasuryId_0='?' (DbType = Int32), @__fromDate_1='?' (DbType = DateTime2), @__toDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[ActionTypeId], [t].[Amount], [t].[CreatedAt], [t].[CreatedBy], [t].[Description], [t].[FinancialTransactionId], [t].[ImagePath], [t].[IsActive], [t].[IsDeleted], [t].[Notes], [t].[ReferenceId], [t].[ReferenceNumber], [t].[ReferenceType], [t].[TransactionDate], [t].[TreasuryId], [t].[UpdatedAt], [t].[UpdatedBy], [m].[Id], [m].[ActionTypeId], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[IsActive], [m].[IsDeleted], [m].[NameEn], [m].[ParentActionId], [m].[UpdatedAt], [m].[UpdatedBy], [a].[Id], [a].[CreatedAt], [a].[CreatedBy], [a].[Description], [a].[IsActive], [a].[IsDeleted], [a].[NameEn], [a].[UpdatedAt], [a].[UpdatedBy]
FROM [TreasuryTransactions] AS [t]
INNER JOIN [MainActions] AS [m] ON [t].[ActionTypeId] = [m].[Id]
LEFT JOIN [ActionTypes] AS [a] ON [m].[ActionTypeId] = [a].[Id]
WHERE [t].[TreasuryId] = @__treasuryId_0 AND [t].[IsDeleted] = CAST(0 AS bit) AND [t].[TransactionDate] >= @__fromDate_1 AND [t].[TransactionDate] <= @__toDate_2
2025-06-25 14:45:00.276 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'GamalCompany.Data.Context.ApplicationDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'ReferenceId'.
Invalid column name 'ReferenceType'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:e061c3e8-92d0-4b9f-b2dc-c07b0e2bb424
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'ReferenceId'.
Invalid column name 'ReferenceType'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:e061c3e8-92d0-4b9f-b2dc-c07b0e2bb424
Error Number:207,State:1,Class:16
2025-06-25 14:45:00.342 +03:00 [ERR] Error calculating treasury balance: 1
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'ReferenceId'.
Invalid column name 'ReferenceType'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at GamalComapany.Service.Repositories.Implementations.FinancialService.GetTreasuryBalanceAsync(Int32 treasuryId, DateTime fromDate, DateTime toDate) in D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapany.Service\Repositories\Implementations\FinancialService.cs:line 429
ClientConnectionId:e061c3e8-92d0-4b9f-b2dc-c07b0e2bb424
Error Number:207,State:1,Class:16
2025-06-25 14:45:00.350 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'GamalComapany.Service.Dtos.ApiResponse`1[[GamalComapany.Service.Dtos.FinancialDto.TreasuryBalanceDto, GamalComapany.Service, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 14:45:00.352 +03:00 [INF] Executed action GamalComapnyApp.API.Controllers.FinancialController.GetTreasuryBalance (GamalComapnyApp.API) in 89.4137ms
2025-06-25 14:45:00.354 +03:00 [INF] Executed endpoint 'GamalComapnyApp.API.Controllers.FinancialController.GetTreasuryBalance (GamalComapnyApp.API)'
2025-06-25 14:45:00.355 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/api/Financial/treasuries/1/balance?fromDate=2024-01-01&toDate=2026-01-01 - 400 null application/json; charset=utf-8 99.7306ms
2025-06-25 16:27:17.968 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-25 16:27:18.257 +03:00 [INF] Now listening on: http://localhost:5250
2025-06-25 16:27:18.327 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 16:27:18.329 +03:00 [INF] Hosting environment: Development
2025-06-25 16:27:18.330 +03:00 [INF] Content root path: D:\AIProjectTest\Sourcs\GamalCompanyApp\GamalComapnyApp\GamalComapnyApp
2025-06-25 16:27:48.967 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/index.html - null null
2025-06-25 16:27:49.233 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/index.html - 200 null text/html;charset=utf-8 272.9424ms
2025-06-25 16:27:49.263 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - null null
2025-06-25 16:27:49.270 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_framework/aspnetcore-browser-refresh.js - 200 16523 application/javascript; charset=utf-8 6.9877ms
2025-06-25 16:27:49.276 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/_vs/browserLink - null null
2025-06-25 16:27:49.319 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/_vs/browserLink - 200 null text/javascript; charset=UTF-8 43.3094ms
2025-06-25 16:27:49.608 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - null null
2025-06-25 16:27:49.774 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5250/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 166.4794ms
