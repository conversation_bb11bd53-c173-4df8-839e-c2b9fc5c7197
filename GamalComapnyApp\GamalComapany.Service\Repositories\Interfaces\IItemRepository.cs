﻿using GamalComapany.Service.Dtos;
using GamalComapany.Service.Dtos.ItemDto;

namespace GamalComapany.Service.Repositories.Interfaces
{
    public interface IItemRepository
    {
        Task<ApiResponse<List<ItemDto>>> GetItems();
        Task<ApiResponse<ItemDto>> GetItemById(int id);
        Task<ApiResponse<ItemDto>> CreateItem(CreateItemDto createItemDto);
        Task<ApiResponse<ItemDto>> UpdateItem(UpdateItemItemDto updateItemDto);
        Task<ApiResponse<bool>> DeleteItem(int id);
    }
}
