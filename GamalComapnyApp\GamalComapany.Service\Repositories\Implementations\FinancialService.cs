using AutoMapper;
using GamalComapany.Service.Dtos;
using GamalComapany.Service.Dtos.FinancialDto;
using GamalComapany.Service.Repositories.Interfaces;
using GamalComapny.Service.Context;
using GamalCompany.Data.Enum;
using GamalCompany.Data.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace GamalComapany.Service.Repositories.Implementations
{
    public class FinancialService : ResponseHandler, IFinancialRepository
    {
        private readonly IUnitOfWorkOfService _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<FinancialService> _logger;
        private readonly IUserContext _userContext;

        public FinancialService(
            IUnitOfWorkOfService unitOfWork,
            IMapper mapper,
            ILogger<FinancialService> logger,
            IUserContext userContext)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
            _userContext = userContext;
        }

        #region Treasury Management

        public async Task<ApiResponse<List<TreasuryResponseDto>>> GetAllTreasuriesAsync()
        {
            try
            {
                var treasuries = await _unitOfWork.Treasuries
                    .GetTableNoTracking()
                    .Where(t => !t.IsDeleted && t.IsActive)
                    .ToListAsync();

                var result = _mapper.Map<List<TreasuryResponseDto>>(treasuries);

                _logger.LogInformation("Retrieved {Count} treasuries", result.Count);
                return Success(result, "تم استرداد الخزائن بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving treasuries");
                return BadRequest<List<TreasuryResponseDto>>("حدث خطأ أثناء استرداد الخزائن");
            }
        }

        public async Task<ApiResponse<TreasuryResponseDto>> GetTreasuryByIdAsync(int id)
        {
            try
            {
                var treasury = await _unitOfWork.Treasuries
                    .GetByIdAsync(id);

                if (treasury == null || treasury.IsDeleted)
                    return NotFound<TreasuryResponseDto>("الخزينة غير موجودة");

                var result = _mapper.Map<TreasuryResponseDto>(treasury);
                return Success(result, "تم استرداد الخزينة بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving treasury: {TreasuryId}", id);
                return BadRequest<TreasuryResponseDto>("حدث خطأ أثناء استرداد الخزينة");
            }
        }

        public async Task<ApiResponse<TreasuryResponseDto>> CreateTreasuryAsync(CreateTreasuryDto treasuryDto)
        {
            try
            {
                // Check if treasury name already exists
                var existingTreasury = await _unitOfWork.Treasuries
                    .GetTableNoTracking()
                    .FirstOrDefaultAsync(t => t.NameEn == treasuryDto.NameEn && !t.IsDeleted);

                if (existingTreasury != null)
                    return BadRequest<TreasuryResponseDto>("اسم الخزينة موجود بالفعل");

                var treasury = _mapper.Map<Treasury>(treasuryDto);
                // CreatedAt and CreatedBy will be set automatically by DbContext audit trail

                await _unitOfWork.Treasuries.AddAsync(treasury);
                await _unitOfWork.SaveChangesAsync();

                var result = _mapper.Map<TreasuryResponseDto>(treasury);

                _logger.LogInformation("Treasury created successfully: {TreasuryId} - {TreasuryName}",
                    treasury.Id, treasury.NameEn);

                return Success(result, "تم إنشاء الخزينة بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating treasury");
                return BadRequest<TreasuryResponseDto>("حدث خطأ أثناء إنشاء الخزينة");
            }
        }

        public async Task<ApiResponse<TreasuryResponseDto>> UpdateTreasuryAsync(UpdateTreasuryDto treasuryDto)
        {
            try
            {
                var existingTreasury = await _unitOfWork.Treasuries.GetByIdAsync(treasuryDto.Id);
                if (existingTreasury == null || existingTreasury.IsDeleted)
                    return NotFound<TreasuryResponseDto>("الخزينة غير موجودة");

                // Check if new name conflicts with existing treasury
                var nameConflict = await _unitOfWork.Treasuries
                    .GetTableNoTracking()
                    .FirstOrDefaultAsync(t => t.NameEn == treasuryDto.NameEn &&
                                                t.Id != treasuryDto.Id && !t.IsDeleted);

                if (nameConflict != null)
                    return BadRequest<TreasuryResponseDto>("اسم الخزينة موجود بالفعل");

                _mapper.Map(treasuryDto, existingTreasury);
                // UpdatedAt and UpdatedBy will be set automatically by DbContext audit trail

                await _unitOfWork.Treasuries.UpdateAsync(existingTreasury);
                await _unitOfWork.SaveChangesAsync();

                var result = _mapper.Map<TreasuryResponseDto>(existingTreasury);

                _logger.LogInformation("Treasury updated successfully: {TreasuryId}", treasuryDto.Id);
                return Success(result, "تم تحديث الخزينة بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating treasury: {TreasuryId}", treasuryDto.Id);
                return BadRequest<TreasuryResponseDto>("حدث خطأ أثناء تحديث الخزينة");
            }
        }

        public async Task<ApiResponse<bool>> DeleteTreasuryAsync(int id)
        {
            try
            {
                var treasury = await _unitOfWork.Treasuries.GetByIdAsync(id);
                if (treasury == null || treasury.IsDeleted)
                    return NotFound<bool>("الخزينة غير موجودة");

                // Check if treasury has transactions
                var hasTransactions = await _unitOfWork.TreasuryTransactions
                    .GetTableNoTracking()
                    .AnyAsync(tt => tt.TreasuryId == id && !tt.IsDeleted);

                if (hasTransactions)
                    return BadRequest<bool>("لا يمكن حذف الخزينة لوجود معاملات مرتبطة بها");

                // Soft delete
                treasury.IsDeleted = true;
                treasury.IsActive = false;
                // UpdatedAt and UpdatedBy will be set automatically by DbContext audit trail

                await _unitOfWork.Treasuries.UpdateAsync(treasury);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Treasury deleted successfully: {TreasuryId}", id);
                return Success(true, "تم حذف الخزينة بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting treasury: {TreasuryId}", id);
                return BadRequest<bool>("حدث خطأ أثناء حذف الخزينة");
            }
        }

        #endregion

        #region Treasury Transactions

        public async Task<ApiResponse<List<TreasuryTransactionResponseDto>>> GetTreasuryTransactionsAsync(
            int? treasuryId = null, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var query = _unitOfWork.TreasuryTransactions.GetTableNoTracking()
                    .Where(tt => !tt.IsDeleted)
                    .Include(tt => tt.Treasury)
                    .Include(tt => tt.MainAction)
                    .ThenInclude(ma => ma.ActionTypes)
                    .AsQueryable();

                if (treasuryId.HasValue)
                    query = query.Where(tt => tt.TreasuryId == treasuryId.Value);

                if (fromDate.HasValue)
                    query = query.Where(tt => tt.TransactionDate >= fromDate.Value);

                if (toDate.HasValue)
                    query = query.Where(tt => tt.TransactionDate <= toDate.Value);

                var transactions = await query
                    .OrderByDescending(tt => tt.TransactionDate)
                    .ThenByDescending(tt => tt.Id)
                    .ToListAsync();

                var result = _mapper.Map<List<TreasuryTransactionResponseDto>>(transactions);

                // Calculate running balance for each treasury
                CalculateRunningBalances(result);

                _logger.LogInformation("Retrieved {Count} treasury transactions", result.Count);
                return Success(result, "تم استرداد معاملات الخزينة بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving treasury transactions");
                return BadRequest<List<TreasuryTransactionResponseDto>>("حدث خطأ أثناء استرداد معاملات الخزينة");
            }
        }

        private void CalculateRunningBalances(List<TreasuryTransactionResponseDto> transactions)
        {
            var treasuryGroups = transactions.GroupBy(t => t.TreasuryId);

            foreach (var group in treasuryGroups)
            {
                decimal runningBalance = 0;
                var treasuryTransactions = group.OrderBy(t => t.TransactionDate).ThenBy(t => t.Id);

                foreach (var transaction in treasuryTransactions)
                {
                    // Assuming ActionTypeId 1 = In (Add), 2 = Out (Subtract)
                    if (transaction.ActionTypeName == "In")
                        runningBalance += transaction.Amount;
                    else if (transaction.ActionTypeName == "Out")
                        runningBalance -= transaction.Amount;

                    transaction.RunningBalance = runningBalance;
                }
            }
        }

        public async Task<ApiResponse<TreasuryTransactionResponseDto>> CreateTreasuryTransactionAsync(CreateTreasuryTransactionDto transactionDto)
        {
            try
            {
                // Validate treasury exists
                var treasury = await _unitOfWork.Treasuries.GetByIdAsync(transactionDto.TreasuryId);
                if (treasury == null || treasury.IsDeleted)
                    return NotFound<TreasuryTransactionResponseDto>("الخزينة غير موجودة");

                // Validate financial transaction exists
                var financialTransaction = await _unitOfWork.FinancialTransactions.GetByIdAsync(transactionDto.FinancialTransactionId);
                if (financialTransaction == null || financialTransaction.IsDeleted)
                    return NotFound<TreasuryTransactionResponseDto>("المعاملة المالية غير موجودة");

                // Validate action type exists
                var actionType = await _unitOfWork.MainActions.GetByIdAsync(transactionDto.ActionTypeId);
                if (actionType == null || actionType.IsDeleted)
                    return NotFound<TreasuryTransactionResponseDto>("نوع العملية غير موجود");

                var transaction = _mapper.Map<TreasuryTransaction>(transactionDto);
                // CreatedAt and CreatedBy will be set automatically by DbContext audit trail

                await _unitOfWork.TreasuryTransactions.AddAsync(transaction);
                await _unitOfWork.SaveChangesAsync();

                // Get the created transaction with includes
                var createdTransaction = await _unitOfWork.TreasuryTransactions.GetTableNoTracking()
                    .Include(tt => tt.Treasury)
                    .Include(tt => tt.MainAction)
                    .ThenInclude(ma => ma.ActionTypes)
                    .FirstOrDefaultAsync(tt => tt.Id == transaction.Id);

                var result = _mapper.Map<TreasuryTransactionResponseDto>(createdTransaction);

                _logger.LogInformation("Treasury transaction created successfully: {TransactionId} - Treasury: {TreasuryId}",
                    transaction.Id, transactionDto.TreasuryId);

                return Success(result, "تم إنشاء معاملة الخزينة بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating treasury transaction");
                return BadRequest<TreasuryTransactionResponseDto>("حدث خطأ أثناء إنشاء معاملة الخزينة");
            }
        }

        public async Task<ApiResponse<bool>> DeleteTreasuryTransactionAsync(int transactionId)
        {
            try
            {
                var transaction = await _unitOfWork.TreasuryTransactions.GetByIdAsync(transactionId);
                if (transaction == null || transaction.IsDeleted)
                    return NotFound<bool>("معاملة الخزينة غير موجودة");

                // Soft delete
                transaction.IsDeleted = true;
                // UpdatedAt and UpdatedBy will be set automatically by DbContext audit trail

                await _unitOfWork.TreasuryTransactions.UpdateAsync(transaction);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Treasury transaction deleted successfully: {TransactionId}", transactionId);
                return Success(true, "تم حذف معاملة الخزينة بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting treasury transaction: {TransactionId}", transactionId);
                return BadRequest<bool>("حدث خطأ أثناء حذف معاملة الخزينة");
            }
        }

        #endregion

        #region Financial Transactions

        public async Task<ApiResponse<List<FinancialTransactionResponseDto>>> GetFinancialTransactionsAsync(
            DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var query = _unitOfWork.FinancialTransactions.GetTableNoTracking()
                    .Where(ft => !ft.IsDeleted)
                    .Include(ft => ft.TransactionType)
                    .AsQueryable();

                if (fromDate.HasValue)
                    query = query.Where(ft => ft.TransactionDate >= fromDate.Value);

                if (toDate.HasValue)
                    query = query.Where(ft => ft.TransactionDate <= toDate.Value);

                var transactions = await query
                    .OrderByDescending(ft => ft.TransactionDate)
                    .ThenByDescending(ft => ft.Id)
                    .ToListAsync();

                var result = _mapper.Map<List<FinancialTransactionResponseDto>>(transactions);

                _logger.LogInformation("Retrieved {Count} financial transactions", result.Count);
                return Success(result, "تم استرداد المعاملات المالية بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving financial transactions");
                return BadRequest<List<FinancialTransactionResponseDto>>("حدث خطأ أثناء استرداد المعاملات المالية");
            }
        }

        public async Task<ApiResponse<FinancialTransactionResponseDto>> CreateFinancialTransactionAsync(CreateFinancialTransactionDto transactionDto)
        {
            try
            {
                // Validate transaction type exists
                var transactionType = await _unitOfWork.MainActions.GetByIdAsync(transactionDto.TransactionTypeId);
                if (transactionType == null || transactionType.IsDeleted)
                    return NotFound<FinancialTransactionResponseDto>("نوع المعاملة غير موجود");

                var transaction = _mapper.Map<FinancialTransaction>(transactionDto);
                // CreatedAt and CreatedBy will be set automatically by DbContext audit trail

                await _unitOfWork.FinancialTransactions.AddAsync(transaction);
                await _unitOfWork.SaveChangesAsync();

                // Get the created transaction with includes
                var createdTransaction = await _unitOfWork.FinancialTransactions.GetTableNoTracking()
                    .Include(ft => ft.TransactionType)
                    .FirstOrDefaultAsync(ft => ft.Id == transaction.Id);

                var result = _mapper.Map<FinancialTransactionResponseDto>(createdTransaction);

                _logger.LogInformation("Financial transaction created successfully: {TransactionId} - Type: {TransactionTypeId}",
                    transaction.Id, transactionDto.TransactionTypeId);

                return Success(result, "تم إنشاء المعاملة المالية بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating financial transaction");
                return BadRequest<FinancialTransactionResponseDto>("حدث خطأ أثناء إنشاء المعاملة المالية");
            }
        }

        public async Task<ApiResponse<FinancialTransactionResponseDto>> CreateGeneralFinancialAsync(DateTime date, decimal amount, bool IsInFlow, RefranseFinancicalEnum financicalEnum, int refranseId, string descrptions)
        {
            try
            {
                var transaction = new FinancialTransaction()
                {
                    Id = 0,
                    Amount = amount,
                    TransactionDate = date,
                    IsInflow = IsInFlow,
                    ReferenceType = financicalEnum,
                    ReferenceId = refranseId,
                    Description = descrptions,
                    IsDeleted = false,
                    IsActive = true,
                };

                await _unitOfWork.FinancialTransactions.AddAsync(transaction);
                await _unitOfWork.SaveChangesAsync();
                // Get the created transaction with includes
                var createdTransaction = await _unitOfWork.FinancialTransactions.GetTableNoTracking()
                    .Include(ft => ft.TransactionType)
                    .FirstOrDefaultAsync(ft => ft.Id == transaction.Id);

                var result = _mapper.Map<FinancialTransactionResponseDto>(createdTransaction);



                return Success(result, "تم إنشاء المعاملة المالية بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating financial transaction");
                return BadRequest<FinancialTransactionResponseDto>("حدث خطأ أثناء إنشاء المعاملة المالية");
            }

        }


        public async Task<ApiResponse<bool>> DeleteFinancialTransactionAsync(int transactionId)
        {
            try
            {
                var transaction = await _unitOfWork.FinancialTransactions.GetByIdAsync(transactionId);
                if (transaction == null || transaction.IsDeleted)
                    return NotFound<bool>("المعاملة المالية غير موجودة");

                // Check if transaction is linked to treasury transactions
                var hasLinkedTransactions = await _unitOfWork.TreasuryTransactions
                    .GetTableNoTracking()
                    .AnyAsync(tt => tt.FinancialTransactionId == transactionId && !tt.IsDeleted);

                if (hasLinkedTransactions)
                    return BadRequest<bool>("لا يمكن حذف المعاملة المالية لوجود معاملات خزينة مرتبطة بها");

                // Soft delete
                transaction.IsDeleted = true;
                // UpdatedAt and UpdatedBy will be set automatically by DbContext audit trail

                await _unitOfWork.FinancialTransactions.UpdateAsync(transaction);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Financial transaction deleted successfully: {TransactionId}", transactionId);
                return Success(true, "تم حذف المعاملة المالية بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting financial transaction: {TransactionId}", transactionId);
                return BadRequest<bool>("حدث خطأ أثناء حذف المعاملة المالية");
            }
        }

        #endregion

        #region Treasury Balance and Reports

        public async Task<ApiResponse<TreasuryBalanceDto>> GetTreasuryBalanceAsync(int treasuryId, DateTime fromDate, DateTime toDate)
        {
            try
            {
                var treasury = await _unitOfWork.Treasuries.GetByIdAsync(treasuryId);
                if (treasury == null || treasury.IsDeleted)
                    return NotFound<TreasuryBalanceDto>("الخزينة غير موجودة");

                var transactions = await _unitOfWork.TreasuryTransactions.GetTableNoTracking()
                    .Where(tt => tt.TreasuryId == treasuryId &&
                                !tt.IsDeleted &&
                                tt.TransactionDate >= fromDate &&
                                tt.TransactionDate <= toDate)
                    .Include(tt => tt.MainAction)
                    .ThenInclude(ma => ma.ActionTypes)
                    .ToListAsync();

                var openingBalance = await GetTreasuryBalanceBeforeDate(treasuryId, fromDate);
                var totalIncome = transactions.Where(t => t.MainAction.ActionTypes.NameEn == "In").Sum(t => t.Amount);
                var totalExpense = transactions.Where(t => t.MainAction.ActionTypes.NameEn == "Out").Sum(t => t.Amount);
                var closingBalance = openingBalance + totalIncome - totalExpense;

                var result = new TreasuryBalanceDto
                {
                    TreasuryId = treasuryId,
                    TreasuryName = treasury.NameEn,
                    FromDate = fromDate,
                    ToDate = toDate,
                    OpeningBalance = openingBalance,
                    TotalIncome = totalIncome,
                    TotalExpense = totalExpense,
                    ClosingBalance = closingBalance
                };

                return Success(result, "تم حساب رصيد الخزينة بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating treasury balance: {TreasuryId}", treasuryId);
                return BadRequest<TreasuryBalanceDto>("حدث خطأ أثناء حساب رصيد الخزينة");
            }
        }

        public async Task<ApiResponse<List<TreasuryBalanceDto>>> GetAllTreasuriesBalanceAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var treasuries = await _unitOfWork.Treasuries
                    .GetTableNoTracking()
                    .Where(t => !t.IsDeleted && t.IsActive)
                    .ToListAsync();

                var result = new List<TreasuryBalanceDto>();

                foreach (var treasury in treasuries)
                {
                    var balanceResponse = await GetTreasuryBalanceAsync(treasury.Id, fromDate, toDate);
                    if (balanceResponse.Succeeded && balanceResponse.Data != null)
                    {
                        result.Add(balanceResponse.Data);
                    }
                }

                _logger.LogInformation("Retrieved balance for {Count} treasuries", result.Count);
                return Success(result, "تم حساب أرصدة جميع الخزائن بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating all treasuries balance");
                return BadRequest<List<TreasuryBalanceDto>>("حدث خطأ أثناء حساب أرصدة الخزائن");
            }
        }

        public async Task<ApiResponse<CashFlowDto>> GetCashFlowReportAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var transactions = await _unitOfWork.TreasuryTransactions.GetTableNoTracking()
                    .Where(tt => !tt.IsDeleted &&
                                tt.TransactionDate >= fromDate &&
                                tt.TransactionDate <= toDate)
                    .Include(tt => tt.MainAction)
                    .ThenInclude(ma => ma.ActionTypes)
                    .ToListAsync();

                var incomeTransactions = transactions.Where(t => t.MainAction.ActionTypes.NameEn == "In");
                var expenseTransactions = transactions.Where(t => t.MainAction.ActionTypes.NameEn == "Out");

                var totalIncome = incomeTransactions.Sum(t => t.Amount);
                var totalExpense = expenseTransactions.Sum(t => t.Amount);

                var incomeCategories = incomeTransactions
                    .GroupBy(t => t.MainAction.NameEn)
                    .Select(g => new CashFlowCategoryDto
                    {
                        CategoryName = g.Key,
                        Amount = g.Sum(t => t.Amount),
                        Percentage = totalIncome > 0 ? (g.Sum(t => t.Amount) / totalIncome) * 100 : 0
                    }).ToList();

                var expenseCategories = expenseTransactions
                    .GroupBy(t => t.MainAction.NameEn)
                    .Select(g => new CashFlowCategoryDto
                    {
                        CategoryName = g.Key,
                        Amount = g.Sum(t => t.Amount),
                        Percentage = totalExpense > 0 ? (g.Sum(t => t.Amount) / totalExpense) * 100 : 0
                    }).ToList();

                var result = new CashFlowDto
                {
                    FromDate = fromDate,
                    ToDate = toDate,
                    TotalIncome = totalIncome,
                    TotalExpense = totalExpense,
                    NetCashFlow = totalIncome - totalExpense,
                    IncomeCategories = incomeCategories,
                    ExpenseCategories = expenseCategories
                };

                _logger.LogInformation("Generated cash flow report for period {FromDate} to {ToDate}", fromDate, toDate);
                return Success(result, "تم إنشاء تقرير التدفق النقدي بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating cash flow report");
                return BadRequest<CashFlowDto>("حدث خطأ أثناء إنشاء تقرير التدفق النقدي");
            }
        }

        public async Task<ApiResponse<decimal>> GetTreasuryCurrentBalanceAsync(int treasuryId)
        {
            try
            {
                var treasury = await _unitOfWork.Treasuries.GetByIdAsync(treasuryId);
                if (treasury == null || treasury.IsDeleted)
                    return NotFound<decimal>("الخزينة غير موجودة");

                var transactions = await _unitOfWork.TreasuryTransactions.GetTableNoTracking()
                    .Where(tt => tt.TreasuryId == treasuryId && !tt.IsDeleted)
                    .Include(tt => tt.MainAction)
                    .ThenInclude(ma => ma.ActionTypes)
                    .ToListAsync();

                var balance = transactions.Sum(t =>
                    t.MainAction.ActionTypes.NameEn == "In" ? t.Amount :
                    t.MainAction.ActionTypes.NameEn == "Out" ? -t.Amount : 0);

                return Success(balance, "تم حساب الرصيد الحالي بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating current treasury balance: {TreasuryId}", treasuryId);
                return BadRequest<decimal>("حدث خطأ أثناء حساب الرصيد الحالي");
            }
        }

        public async Task<ApiResponse<decimal>> GetTotalCashBalanceAsync()
        {
            try
            {
                var treasuries = await _unitOfWork.Treasuries
                    .GetTableNoTracking()
                    .Where(t => !t.IsDeleted && t.IsActive)
                    .ToListAsync();

                decimal totalBalance = 0;

                foreach (var treasury in treasuries)
                {
                    var balanceResponse = await GetTreasuryCurrentBalanceAsync(treasury.Id);
                    if (balanceResponse.Succeeded)
                    {
                        totalBalance += balanceResponse.Data;
                    }
                }

                return Success(totalBalance, "تم حساب إجمالي الرصيد النقدي بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating total cash balance");
                return BadRequest<decimal>("حدث خطأ أثناء حساب إجمالي الرصيد النقدي");
            }
        }

        private async Task<decimal> GetTreasuryBalanceBeforeDate(int treasuryId, DateTime date)
        {
            var transactions = await _unitOfWork.TreasuryTransactions.GetTableNoTracking()
                .Where(tt => tt.TreasuryId == treasuryId &&
                            !tt.IsDeleted &&
                            tt.TransactionDate < date)
                .Include(tt => tt.MainAction)
                .ThenInclude(ma => ma.ActionTypes)
                .ToListAsync();

            return transactions.Sum(t =>
                t.MainAction.ActionTypes.NameEn == "In" ? t.Amount :
                t.MainAction.ActionTypes.NameEn == "Out" ? -t.Amount : 0);
        }



        #endregion
    }
}
