[{"ContainingType": "GamalComapnyApp.API.Controllers.CampanyController", "Method": "GetCompany", "RelativePath": "api/Campany", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.CampanyController", "Method": "AddCompany", "RelativePath": "api/Campany", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "company", "Type": "GamalComapany.Service.Dtos.CompanyDto.CreateCompanyDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.CampanyController", "Method": "UpdateCompany", "RelativePath": "api/Campany", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "company", "Type": "GamalComapany.Service.Dtos.CompanyDto.UpdateCompanyDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.CampanyController", "Method": "AddDepartment", "RelativePath": "api/Campany/department", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "depatment", "Type": "GamalComapany.Service.Dtos.CompanyDto.CearteDepartmentDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.CampanyController", "Method": "UpdateDepartment", "RelativePath": "api/Campany/department", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "depatmentcompany", "Type": "GamalComapany.Service.Dtos.CompanyDto.UpdateDepartmentDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.CampanyController", "Method": "GetListOFDepartment", "RelativePath": "api/Campany/department", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.CategroyController", "Method": "GetCategory", "RelativePath": "api/Categroy", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.FinancialController", "Method": "GetBalanceSheet", "RelativePath": "api/Financial/reports/balance-sheet", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "asOfDate", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.FinancialController", "Method": "GetCashFlowReport", "RelativePath": "api/Financial/reports/cash-flow", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fromDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "toDate", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.FinancialController", "Method": "GetProfitLossStatement", "RelativePath": "api/Financial/reports/profit-loss", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fromDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "toDate", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.FinancialController", "Method": "GetTotalCashBalance", "RelativePath": "api/Financial/total-cash-balance", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.FinancialController", "Method": "GetFinancialTransactions", "RelativePath": "api/Financial/transactions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "toDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.FinancialController", "Method": "CreateFinancialTransaction", "RelativePath": "api/Financial/transactions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "transactionDto", "Type": "GamalComapany.Service.Dtos.FinancialDto.CreateFinancialTransactionDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.FinancialController", "Method": "DeleteFinancialTransaction", "RelativePath": "api/Financial/transactions/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.FinancialController", "Method": "GetAllTreasuries", "RelativePath": "api/Financial/treasuries", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.FinancialController", "Method": "CreateTreasury", "RelativePath": "api/Financial/treasuries", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "treasuryDto", "Type": "GamalComapany.Service.Dtos.FinancialDto.CreateTreasuryDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.FinancialController", "Method": "UpdateTreasury", "RelativePath": "api/Financial/treasuries", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "treasuryDto", "Type": "GamalComapany.Service.Dtos.FinancialDto.UpdateTreasuryDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.FinancialController", "Method": "GetTreasuryById", "RelativePath": "api/Financial/treasuries/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.FinancialController", "Method": "DeleteTreasury", "RelativePath": "api/Financial/treasuries/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.FinancialController", "Method": "GetTreasuryBalance", "RelativePath": "api/Financial/treasuries/{treasuryId}/balance", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "treasuryId", "Type": "System.Int32", "IsRequired": true}, {"Name": "fromDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "toDate", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.FinancialController", "Method": "GetTreasuryCurrentBalance", "RelativePath": "api/Financial/treasuries/{treasuryId}/current-balance", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "treasuryId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.FinancialController", "Method": "GetAllTreasuriesBalance", "RelativePath": "api/Financial/treasuries/balance", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fromDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "toDate", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.FinancialController", "Method": "GetTreasuryTransactions", "RelativePath": "api/Financial/treasury-transactions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "treasuryId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "fromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "toDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.FinancialController", "Method": "CreateTreasuryTransaction", "RelativePath": "api/Financial/treasury-transactions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "transactionDto", "Type": "GamalComapany.Service.Dtos.FinancialDto.CreateTreasuryTransactionDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.FinancialController", "Method": "DeleteTreasuryTransaction", "RelativePath": "api/Financial/treasury-transactions/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.ImageController", "Method": "DeleteImage", "RelativePath": "api/Image", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "imagePath", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.ImageController", "Method": "GetImage", "RelativePath": "api/Image/{imagePath}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "imagePath", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.ImageController", "Method": "CleanupOrphanedImages", "RelativePath": "api/Image/cleanup", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.ImageController", "Method": "ImageExists", "RelativePath": "api/Image/exists", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "imagePath", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.ImageController", "Method": "GetSupportedFormats", "RelativePath": "api/Image/formats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.ImageController", "Method": "GetMaxFileSize", "RelativePath": "api/Image/max-size", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.ImageController", "Method": "UploadProfileImage", "RelativePath": "api/Image/profile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.ImageController", "Method": "DeleteProfileImage", "RelativePath": "api/Image/profile", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.ImageController", "Method": "ResizeImage", "RelativePath": "api/Image/resize", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "GamalComapnyApp.API.Controllers.ResizeImageRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.ImageController", "Method": "UploadImage", "RelativePath": "api/Image/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "folder", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.InventoryController", "Method": "UpdateItem", "RelativePath": "api/Inventory/items", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "updateItemDto", "Type": "GamalComapany.Service.Dtos.InventoryDto.UpdateItemDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.InventoryController", "Method": "GenerateBarcode", "RelativePath": "api/Inventory/items/{itemId}/generate-barcode", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.InventoryController", "Method": "GetItemImages", "RelativePath": "api/Inventory/items/{itemId}/images", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.InventoryController", "Method": "UploadItemImage", "RelativePath": "api/Inventory/items/{itemId}/images", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.Int32", "IsRequired": true}, {"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "is<PERSON><PERSON>", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.InventoryController", "Method": "SetMainImage", "RelativePath": "api/Inventory/items/{itemId}/images/{imageId}/set-main", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.Int32", "IsRequired": true}, {"Name": "imageId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.InventoryController", "Method": "GetItemsByCategory", "RelativePath": "api/Inventory/items/category/{categoryId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.InventoryController", "Method": "ExportItems", "RelativePath": "api/Inventory/items/export", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.InventoryController", "Method": "DeleteItemImage", "RelativePath": "api/Inventory/items/images/{imageId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "imageId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.InventoryController", "Method": "SearchItems", "RelativePath": "api/Inventory/items/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchTerm", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.InventoryController", "Method": "GetInventoryMovementReport", "RelativePath": "api/Inventory/movement-report", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fromDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "toDate", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.InventoryController", "Method": "GetInventoryMovement", "RelativePath": "api/Inventory/movement/{itemId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.Int32", "IsRequired": true}, {"Name": "fromDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "toDate", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.InventoryController", "Method": "GetItemsStock", "RelativePath": "api/Inventory/stock", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.InventoryController", "Method": "GetItemStock", "RelativePath": "api/Inventory/stock/{itemId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.InventoryController", "Method": "GetLowStockItems", "RelativePath": "api/Inventory/stock/low", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.InventoryController", "Method": "GetInventoryTransactions", "RelativePath": "api/Inventory/transactions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "fromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "toDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.InventoryController", "Method": "CreateInventoryTransaction", "RelativePath": "api/Inventory/transactions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "transactionDto", "Type": "GamalComapany.Service.Dtos.InventoryDto.CreateInventoryTransactionDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.InventoryController", "Method": "DeleteInventoryTransaction", "RelativePath": "api/Inventory/transactions/{transactionId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "transactionId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.PartnerController", "Method": "GetAllPartners", "RelativePath": "api/Partner", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.PartnerController", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/Partner", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createPartnerDto", "Type": "GamalComapany.Service.Dtos.PartnerDto.CreatePartnerDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.PartnerController", "Method": "Update<PERSON><PERSON><PERSON>", "RelativePath": "api/Partner", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "updatePartnerDto", "Type": "GamalComapany.Service.Dtos.PartnerDto.UpdatePartnerDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.PartnerController", "Method": "GetPartnerById", "RelativePath": "api/Partner/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.PartnerController", "Method": "Delete<PERSON><PERSON>ner", "RelativePath": "api/Partner/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.PartnerController", "Method": "GetPartnerTransactions", "RelativePath": "api/Partner/{partnerId}/transactions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "partnerId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.PartnerController", "Method": "CalculatePartnersCapital", "RelativePath": "api/Partner/capital-calculation", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.PartnerController", "Method": "CalculateProfitDistribution", "RelativePath": "api/Partner/profit-distribution", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "totalProfit", "Type": "System.Decimal", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.PartnerController", "Method": "RecalculateSharePercentages", "RelativePath": "api/Partner/recalculate-shares", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.PartnerController", "Method": "CreatePartnerTransaction", "RelativePath": "api/Partner/transactions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "transactionDto", "Type": "GamalComapany.Service.Dtos.PartnerDto.PartnerTransactionDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.PartnerController", "Method": "DeletePartnerTransaction", "RelativePath": "api/Partner/transactions/{transactionId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "transactionId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UnitController", "Method": "GetUnits", "RelativePath": "api/Unit", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "CreateUser", "RelativePath": "api/User", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userDto", "Type": "GamalComapany.Service.Dtos.UserDto.CreateUserDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "UpdateUser", "RelativePath": "api/User", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "userDto", "Type": "GamalComapany.Service.Dtos.UserDto.UpdateUserDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "GetUserById", "RelativePath": "api/User/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "DeleteUser", "RelativePath": "api/User/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "HasPermission", "RelativePath": "api/User/{userId}/has-permission", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "module", "Type": "System.String", "IsRequired": false}, {"Name": "permission", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "DeleteUserProfileImage", "RelativePath": "api/User/{userId}/image", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "GetUserPermissionSummary", "RelativePath": "api/User/{userId}/permission-summary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "GetUserPermissions", "RelativePath": "api/User/{userId}/permissions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "UploadUserProfileImage", "RelativePath": "api/User/{userId}/upload-image", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}, {"Name": "ProfileImage", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "GetAllUsers", "RelativePath": "api/User/all-users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "ChangePassword", "RelativePath": "api/User/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "changePasswordDto", "Type": "GamalComapany.Service.Dtos.UserDto.ChangePasswordDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "<PERSON><PERSON>", "RelativePath": "api/User/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginDto", "Type": "GamalComapany.Service.Dtos.UserDto.LoginDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "Logout", "RelativePath": "api/User/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "GetAllModules", "RelativePath": "api/User/modules", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "CreateModule", "RelativePath": "api/User/modules", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "moduleDto", "Type": "GamalComapany.Service.Dtos.UserDto.CreateModuleDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "UpdateModule", "RelativePath": "api/User/modules", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "moduleDto", "Type": "GamalComapany.Service.Dtos.UserDto.UpdateModuleDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "GetModuleById", "RelativePath": "api/User/modules/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "DeleteModule", "RelativePath": "api/User/modules/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "CreateUserPermission", "RelativePath": "api/User/permissions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "permissionDto", "Type": "GamalComapany.Service.Dtos.UserDto.CreateUserPermissionDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "DeleteUserPermission", "RelativePath": "api/User/permissions/{permissionId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "permissionId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "GetProfile", "RelativePath": "api/User/profile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "UpdateProfile", "RelativePath": "api/User/profile", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Int32", "IsRequired": false}, {"Name": "FullName", "Type": "System.String", "IsRequired": false}, {"Name": "Phone", "Type": "System.String", "IsRequired": false}, {"Name": "ProfileImage", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "DeleteProfileImage", "RelativePath": "api/User/profile/image", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "UploadProfileImage", "RelativePath": "api/User/profile/upload-image", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ProfileImage", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "RefreshToken", "RelativePath": "api/User/refresh-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "refreshTokenDto", "Type": "GamalComapany.Service.Dtos.UserDto.RefreshTokenDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "ResetPassword", "RelativePath": "api/User/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "GamalComapnyApp.API.Controllers.ResetPasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "GetUserByUsername", "RelativePath": "api/User/username/{username}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "username", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UserController", "Method": "ValidateToken", "RelativePath": "api/User/validate-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[GamalComapnyApp.WeatherForecast, GamalComapnyApp.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]