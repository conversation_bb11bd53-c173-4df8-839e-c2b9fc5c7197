using Microsoft.AspNetCore.Http;

namespace GamalComapany.Service.Services
{
    public interface IFileUploadService
    {
        /// <summary>
        /// Upload a profile image for a user
        /// </summary>
        /// <param name="file">The image file to upload</param>
        /// <param name="userId">The user ID for whom the image is being uploaded</param>
        /// <returns>The relative path to the uploaded image</returns>
        Task<string> UploadProfileImageAsync(IFormFile file, int userId);

        /// <summary>
        /// Delete a profile image
        /// </summary>
        /// <param name="imagePath">The path to the image to delete</param>
        /// <returns>True if deleted successfully</returns>
        Task<bool> DeleteProfileImageAsync(string imagePath);

        /// <summary>
        /// Validate if the uploaded file is a valid image
        /// </summary>
        /// <param name="file">The file to validate</param>
        /// <returns>True if valid image</returns>
        bool IsValidImage(IFormFile file);

        /// <summary>
        /// Get the full physical path for an image
        /// </summary>
        /// <param name="relativePath">The relative path of the image</param>
        /// <returns>Full physical path</returns>
        string GetFullImagePath(string relativePath);
    }
}
