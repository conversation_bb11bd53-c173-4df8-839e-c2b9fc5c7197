﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.0" />
    <PackageReference Include="FluentValidation" Version="11.9.0" />
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.10" />
    <PackageReference Include="SixLabors.ImageSharp.Web" Version="3.1.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\GamalCompany.Data\GamalCompany.Data.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Dtos\PartnerDto\" />
    <Folder Include="Dtos\InventoryDto\" />
    <Folder Include="Dtos\FinancialDto\" />
    <Folder Include="Dtos\UserDto\" />
    <Folder Include="Dtos\SupplierCustomerDto\" />
    <Folder Include="Mapping\" />
    <Folder Include="Validators\" />
  </ItemGroup>

</Project>
